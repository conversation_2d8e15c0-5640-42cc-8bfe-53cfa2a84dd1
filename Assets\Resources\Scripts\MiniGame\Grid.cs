using System;
using System.Collections;
using UnityEngine;
using System.Linq;
using UnityEngine.UI;

public class Grid
{
    public Tile[,] tiles; // 2D array for the tiles
    private readonly float CellSize, x, y;
    private readonly int w, h; // width and height
    private readonly float runeRadius = 0.4f; // radius of the rune
    public bool runeCheck = true; // check if it can check the runes
    public bool changeRunes = true; // check if it can change the runes
    public int[,] tileIDS; // 2D array for the tile ids
    public GridCreator creator; // reference to the GridCreator
    public Score score = new(); // reference to the Score
    public ConfigsHandler configsHandler; // reference to the ConfigsHandler

    private readonly Array Array = Enum.GetValues(typeof(Types)); // array of all the types

    public Grid(float CellSize, int w, int h, float x, float y, GridCreator creator, ConfigsHandler configsHandler)
    {
        // set the values
        this.CellSize = CellSize;
        this.w = w;
        this.h = h;
        this.x = x;
        this.y = -y;
        this.creator = creator;
        this.configsHandler = configsHandler;

        tileIDS = new int[w, h];
        tiles = new Tile[w, h];
    }

    public IEnumerator ConstructTiles() // construct the tiles
    {
        for (float width = 0; width < w; width++)
        {
            for (float height = 0; height < h; height++)
            {
                float tx = width * CellSize + x; // position of the tile on the x axis
                float ty = height * CellSize + y; // position of the tile on the y axis

                tiles[(int)width, (int)height] = new(); // create a new tile

                tiles[(int)width, (int)height].id = (int)width * w + (int)height; // set the id

                tiles[(int)width, (int)height].CreateTile(tx, ty, (Types)UnityEngine.Random.Range(0, Array.Length), -y, creator.gameObject); // create the tile

                tileIDS[(int)width, (int)height] = tiles[(int)width, (int)height].id; // set the id into the array of the runes ids
            }
        }
        yield return null;
    }

    public Vector3 GetCursorPosition() // get the position of the cursor position
    {
        Vector3 cursorPos = Camera.main.ScreenToWorldPoint(Input.GetTouch(0).position);
        cursorPos.z = creator.transform.position.z;
        return cursorPos;
    }


    public bool TryGetTileUnderCursor(out int x, out int y)
    {
        Vector3 cursorPos = GetCursorPosition(); // get the position of the cursor
        
        // calculate the position of the tile
        x = (int)Mathf.Round((cursorPos.x - this.x) / (w * CellSize) * w); 
        y = (int)Mathf.Round((-cursorPos.y - this.y) / (h * CellSize) * h);

        float tx = (cursorPos.x - this.x) / (w * CellSize) * w;
        float ty = (-cursorPos.y - this.y) / (h * CellSize) * h;

        // calculates the distance between the cursor and the tile
        float radius = Mathf.Sqrt(Mathf.Pow(tx - x, 2) + Mathf.Pow(ty - y, 2)); 

        
        return (x >= 0 && x < w && y >= 0 && y < h) && radius <= runeRadius;
    }

    public IEnumerator MatchedTiles() // check if there are matched tiles
    {
        if (runeCheck)
        {
            runeCheck = false;

            for (int W = 0; W < tiles.GetLength(0) - 2; W++) // loop through the tiles to check in the x axis
            {
                for (int H = 0; H < tiles.GetLength(1); H++)
                {
                    if (tiles[W, H].tile.name == tiles[W + 1, H].tile.name && tiles[W, H].tile.name == tiles[W + 2, H].tile.name) // check if the tiles have the same name
                    {
                        if (!tiles[W, H].isDestroyed) // it only exetutes for the first tile in the sequence
                        {
                            // play the rune switch sound effect
                            //AudioSource soundEffects = configsHandler.transform.GetComponent<AudioSource>();
                            //AudioClip runeSwitchClip = Resources.Load<AudioClip>("SFX/RuneClear");
                            //soundEffects.PlayOneShot(runeSwitchClip, configsHandler.sfxVolume.value * configsHandler.mainVolume.value);

                            // add points
                            //score.points++;

                            // damages de enemy with the rune's type
                            configsHandler.RunesDamage(tiles[W, H].type);
                        }
                        // set the tiles as destroyed
                        tiles[W, H].isDestroyed = true;
                        tiles[W + 1, H].isDestroyed = true;
                        tiles[W + 2, H].isDestroyed = true;
                    }
                }
            }
            for (int W = 0; W < tiles.GetLength(0); W++) // loop through the tiles to check in the y axis
            {
                for (int H = 0; H < tiles.GetLength(1) - 2; H++)
                {
                    if (tiles[W, H].tile.name == tiles[W, H + 1].tile.name && tiles[W, H].tile.name == tiles[W, H + 2].tile.name) // check if the tiles have the same name
                    {
                        if (!(tiles[W, H].isDestroyed && tiles[W, H + 1].isDestroyed)) // it only exetutes for the first tile in the sequence
                        {
                            // play the rune switch sound effect
                            //AudioSource soundEffects = configsHandler.transform.GetComponent<AudioSource>();
                            //AudioClip runeSwitchClip = Resources.Load<AudioClip>("SFX/RuneClear");
                            //soundEffects.PlayOneShot(runeSwitchClip, configsHandler.sfxVolume.value * configsHandler.mainVolume.value);

                            // add points
                            //score.points++;

                            // damages de enemy with the rune's type
                            configsHandler.RunesDamage(tiles[W, H].type);
                        }
                        // set the tiles as destroyed
                        tiles[W, H].isDestroyed = true;
                        tiles[W, H + 1].isDestroyed = true;
                        tiles[W, H + 2].isDestroyed = true;
                    }
                }
            }
        }
        yield return null;
    }

    public IEnumerator FixTiles() // fix the tiles position inside the tile array
    {
        var check = Tools.FindIndexes(tiles, true); // gets the indexes of the first destroyed tile

        if (check[0] != -1 && check[1] != -1 && changeRunes) // checks if there are destroyed tiles and if it can change the runes
        {
            // changes to the next change combo if the combo is not infinite and the current combo is not the last
            if (configsHandler.currentCombo < configsHandler.ComboChance.Length - 1 && !configsHandler.infiniteToggle.GetComponent<Toggle>().isOn) configsHandler.currentCombo++;

            //score.combo++;
            //Debug.Log("Score Combo: " + score.combo);

            if (configsHandler.IsThereCharacters()) configsHandler.ppLeft -= configsHandler.ReductionPerCombo;

            int startTileX = -1;
            int startTileY = -1;

            int numOftiles = 0;

            // gets a random value between 0 and 100
            int RDMvalue = UnityEngine.Random.Range(0, 100);
            // gets a random type
            Types newType = (Types)UnityEngine.Random.Range(0, Array.Length);

            // makes a temporary copy of the type
            Types tempType = newType;

            // makes that runes can't be changed
            changeRunes = false;
            for (int W = 0; W < tiles.GetLength(0); W++)
            {
                for (int H = 0; H < tiles.GetLength(1); H++)
                {
                    Tools.NumOfDestroyedTiles(tiles, W, out int num, out int[] notDestroyed); // gets the number of destroyed tiles

                    // makes a copy of the notDestroyed array and sorts it
                    int[] sortedNotDestroyed = new int[notDestroyed.Length];

                    notDestroyed.CopyTo(sortedNotDestroyed, 0);

                    int totalDestroyed = num;

                    Array.Sort(sortedNotDestroyed);
                    for (int i = 0; num != 0; i++)
                    {
                        if (tiles[W, i].isDestroyed)
                        {
                            if (startTileX == -1) startTileX = W; // sets the start tile x
                            if (startTileY == -1) startTileY = i; // sets the start tile y

                            float posY = (i + num) * CellSize + tiles[W, i].tile.transform.position.y + 0.5f; // gets the new y position
                            num--; 

                            // checks if the tile is aligned with the start tile, if it is and the number of aligned tiles is greater than 3, the randon value is greater or equal than the combo chance and the combo is not infinite
                            // it changes the type to a random one
                            if (
                                !(
                                ((W == startTileX && i < startTileY + 3)
                                ||
                                (((i < startTileY && totalDestroyed == num + 1) || (i == startTileY && totalDestroyed == num + 1)) && W < startTileX + 3))
                                &&
                                numOftiles < 3)
                                ||
                                RDMvalue >= configsHandler.ComboChance[configsHandler.currentCombo != -1 ? configsHandler.currentCombo : 0]
                                &&
                                !configsHandler.infiniteToggle.GetComponent<Toggle>().isOn) newType = (Types)UnityEngine.Random.Range(0, Array.Length);

                            else // otherwise it resets the type
                            {
                                numOftiles++;
                                if (newType != tempType) newType = tempType;
                            }

                            // creates a tile destroy animation
                            TileDestroy destroy = new(tiles[W, i].tile.transform.position, creator.FirstColor, creator.SecondColor);
                            // makes the animation
                            creator.DoTileDestroyAnimation(destroy);
                            // changes the tile position
                            tiles[W, i].tile.transform.position = new Vector3(tiles[W, i].tile.transform.position.x, posY, creator.transform.position.z);
                            // changes the tile name
                            tiles[W, i].tile.name = newType.ToString();
                            // changes the tile type
                            tiles[W, i].type = newType;
                            // changes the tile sprite
                            tiles[W, i].tileImg.GetComponent<Image>().sprite = Resources.Load<Sprite>($@"Sprites/UI/{newType}");
                            // sets the tile as not destroyed
                            tiles[W, i].isDestroyed = false;
                        }
                    }
                    while (!notDestroyed.SequenceEqual(sortedNotDestroyed)) // changes the tiles order until the notDestroyed array is sorted
                    {
                        for (int i = 0; i < notDestroyed.Length - 1; i++)
                        {
                            if (notDestroyed[i] != -1 && notDestroyed[i + 1] == -1)
                            {
                                (
                                    tileIDS[W, i],
                                    tileIDS[W, i + 1],
                                    notDestroyed[i],
                                    notDestroyed[i + 1]
                                )
                                =
                                (
                                    tileIDS[W, i + 1],
                                    tileIDS[W, i],
                                    notDestroyed[i + 1],
                                    notDestroyed[i]
                                );
                            }
                        }
                    }
                }
            }
            changeRunes = true;
        }

        yield return null;
    }



    public void MoveTile(int x, int y) // moves the tile to the cursor position
    {
        tiles[x, y].tile.transform.position = GetCursorPosition();
    }
}
