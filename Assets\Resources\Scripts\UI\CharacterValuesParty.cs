using UnityEngine;
using UnityEngine.UI;
using TMPro;
using System.Linq;
using System.Collections.Generic;
using DG.Tweening;

public class CharacterValuesParty : MonoBehaviour
{
    ConfigsHandler configsHandler;
    GameObject cardPrefab;

    CharatersForPartyUIHandler cfUIH;
    PartyConfigs partyConfigs;

    public BattleCharacter character; // the character that this UI represents

    GameObject selectOverlay;
    Button detailsButton;
    Button addButton;

    public bool isSelected = false;


    // Dictionary that controls the colors of the rarities
    private readonly Dictionary<string, Color> RarityColors = new();

    void Start()
    {
        configsHandler = GameObject.Find("GameConfigsHandler").GetComponent<ConfigsHandler>();
        cfUIH = GameObject.Find("PartyConfs").GetComponent<CharatersForPartyUIHandler>();
        partyConfigs = GameObject.Find("PartiesContainer").GetComponent<PartyConfigs>();

        cardPrefab = transform.parent.gameObject;
        selectOverlay = transform.parent.GetChild(0).gameObject;
        detailsButton = selectOverlay.transform.GetChild(0).GetComponent<Button>();
        addButton = selectOverlay.transform.GetChild(2).GetComponent<Button>();

        // Add the listener to the button to insert the character to the party
        GetComponent<Button>().onClick.AddListener(SelectCard);
        addButton.onClick.AddListener(InsertCharaterToTeam);

        // Initialize RarityColors dictionary
        RarityColors.Add("Comum", GeneralInfo.GetTierColor("TR2"));
        RarityColors.Add("Inferior", GeneralInfo.GetTierColor("TR5"));
        RarityColors.Add("Raro", GeneralInfo.GetTierColor("TR3"));
        RarityColors.Add("Épico", GeneralInfo.GetTierColor("TR1"));
        RarityColors.Add("Lendário", GeneralInfo.GetTierColor("TR6"));
        RarityColors.Add("Elementar", GeneralInfo.GetTierColor("TR4"));
        RarityColors.Add("Missing", Color.black);
    }

    void Update()
    {
        // gets the most updated value of the character
        if (character != null) character = configsHandler.GetCharacterByID(character.id);

        // if null destroy the object
        if (character == null) Destroy(gameObject);
        else // otherwise update the values
        {
            transform.GetChild(2).GetComponent<Image>().color = RarityColors[character.rarity];
            transform.GetChild(5).GetComponent<TextMeshProUGUI>().text = character.name;
            transform.GetChild(4).GetChild(0).GetComponent<TextMeshProUGUI>().text = character.level.ToString();
            //transform.GetChild(2).GetComponent<TextMeshProUGUI>().text = "Atk: " + character.stats.GetAtk(character.level).ToString();
        }

        CheckIfInPartyAndDead();


    }

    public void SelectCard()
    {
        // play the select sound effect
        ConfMenu.PlaySoundWithVolume(GameObject.Find("GameConfigsHandler").GetComponent<AudioSource>(), Resources.Load<AudioClip>("SFX/MenuSound"));

        // Deselect all cards
        foreach (var item in transform.parent.parent.parent.GetComponentsInChildren<PlayerCard>()) // disables all the other selected buttons overlay
        {
            item.HideOverlay();
        }
        foreach (var item in transform.parent.parent.GetComponentsInChildren<CharacterValuesParty>()) // disables all the other selected buttons overlay
        {
            if (item != this) item.selectOverlay.SetActive(false);
        }

        PlaySelectBounce();

        //Toggle self
        selectOverlay.SetActive(!selectOverlay.activeSelf);
        isSelected = !isSelected;
        cardPrefab.transform.SetAsLastSibling();

        // Update global selected character
        if (isSelected)
        {
            cfUIH.currentlySelectedCharacter = character;
        }
        else
        {
            if (cfUIH.currentlySelectedCharacter == character)
                cfUIH.currentlySelectedCharacter = null;
        }
    }

    void InsertCharaterToTeam()
    {
        int partyIndex = cfUIH.selectedParty; // gets the selected party

        if (configsHandler.IsCharacterOnParty(character, partyIndex))
        {
            WarningLabel.Instance.ShowMessage("Character already in party");
            return;
        }

        // seach for the first empty slot in the stock
        int slotIndex = configsHandler.GetPartyCharacters(partyIndex).stockCharacters.ToList().FindIndex(c => c == null);

        PartyCharacters party = configsHandler.GetPartyCharacters(partyIndex); // gets the party

        // If all slots are full, do nothing
        if (slotIndex == -1)
        {
            WarningLabel.Instance.ShowMessage("No empty slots in the stock");
            return;
        }

        configsHandler.SetCharacterToParty(partyIndex, slotIndex, false, character); // sets the character to the party in the selected slot
        selectOverlay.SetActive(false);
        partyConfigs.UpdateAllCardsUI();

    }

    void CheckIfInPartyAndDead()
    {
        int partyIndex = cfUIH.selectedParty;

        if (character.IsDead)
        {
            transform.GetChild(10).gameObject.SetActive(true);
            transform.GetChild(10).GetComponent<Image>().color = new Color(1f, 0f, 0f, 0.4f);
            transform.GetChild(10).GetChild(0).GetComponent<TextMeshProUGUI>().text = "<color=white>DEAD</color>";
        }
        else
        {
            if (configsHandler.partyCharacters[partyIndex].activeCharacters.Contains(character) || configsHandler.partyCharacters[partyIndex].stockCharacters.Contains(character))
            {
                transform.GetChild(10).gameObject.SetActive(true);
                transform.GetChild(10).GetChild(0).GetComponent<TextMeshProUGUI>().color = Color.yellow;
                transform.GetChild(10).GetChild(0).GetComponent<TextMeshProUGUI>().text = "<color=yellow>IN PARTY</color>";
            }
            else transform.GetChild(10).gameObject.SetActive(false);
        }
    }
    public void HideOverlay()
    {
        if (selectOverlay != null) selectOverlay.SetActive(false);
    }
    public void ShowOverlay()
    {
        if (selectOverlay != null) selectOverlay.SetActive(true);
    }

    public void ResetCard(BattleCharacter newCharacter)
    {
        character = newCharacter;

        // Check if this character is the currently selected one
        if (cfUIH != null && cfUIH.currentlySelectedCharacter == character)
        {
            isSelected = true;
            ShowOverlay();
        }
        else
        {
            isSelected = false;
            HideOverlay();
        }
    }

        private void PlaySelectBounce()
    {
        // Reset any existing tweens on this transform to avoid stacking
        cardPrefab.transform.DOKill();

        // Start from default scale
        Vector3 initialScale = cardPrefab.transform.localScale;
        //cardPrefab.transform.localScale = Vector3.one;

        // Apply bounce animation
        cardPrefab.transform
            .DOScale(initialScale * 1.1f, 0.15f) // scale up slightly
            .SetEase(Ease.OutQuad)
            .OnComplete(() =>
            {
                cardPrefab.transform
                    .DOScale(initialScale, 0.15f) // scale back to normal
                    .SetEase(Ease.InQuad);
            });
    }
}
