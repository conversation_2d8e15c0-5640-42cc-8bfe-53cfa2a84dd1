using System;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;

public class WeaponIcon : MonoBehaviour
{
    ConfigsHandler configsHandler;
    WeaponsSelectorSrollView weaponSelector;
    GameObject icon;
    Image iconBackground;
    Image iconImage;
    public Weapons weapon;

    BattleCharacter selectedCharacter;

    GameObject selectedPlayerOBJ;

    // Dictionary that controls the colors of the rarities
    private readonly Dictionary<string, Color> RarityColors = new();

    void Awake()
    {
        icon = gameObject.transform.GetChild(0).gameObject;
        iconBackground = icon.GetComponent<Image>();
        iconImage = icon.transform.GetChild(1).GetComponent<Image>();
    }

    // Start is called once before the first execution of Update after the MonoBehaviour is created
    void Start()
    {
        configsHandler = GameObject.Find("GameConfigsHandler").GetComponent<ConfigsHandler>(); // Get the ConfigsHandler script
        weaponSelector = GameObject.Find("WeaponsSelectorSrollView").GetComponent<WeaponsSelectorSrollView>();

        selectedPlayerOBJ = configsHandler.playersInterface[Array.IndexOf(configsHandler.playerSelected, true)]; // gets the selected player GameObject
        transform.position = selectedPlayerOBJ.transform.position + new Vector3(0f, 1f, 0f);
    }

    // Update is called once per frame
    void Update()
    {
        selectedCharacter = configsHandler.GetSelectedPlayerCharacter();
        if (weapon != null && weapon == selectedCharacter.weaponHeld)
        {
            icon.SetActive(true); // Enables the icon
        }
        else
        {
            icon.SetActive(false); // Disables the icon
        }
    }

    public void SetIcon(Sprite sprite, Color color, Weapons weapon)
    {
        // If components aren't initialized yet, try to initialize them
        if (iconBackground == null || iconImage == null)
        {
            icon = gameObject.transform.GetChild(0).gameObject;
            iconBackground = icon.GetComponent<Image>();
            iconImage = icon.transform.GetChild(0).GetComponent<Image>();

            // Check if initialization worked
            if (iconBackground == null || iconImage == null)
            {
                Debug.LogError("Icon components not initialized properly");
                return;
            }
        }

        if (sprite == null)
        {
            Debug.LogWarning("Sprite parameter is null");
            return;
        }

        iconBackground.color = color;
        iconImage.sprite = sprite;
        this.weapon = weapon;
    }

}
