using System.Collections;
using System.Collections.Generic;
using System.Linq;
using DG.Tweening;
using TMPro;
using Unity.VisualScripting;
using UnityEngine;
using UnityEngine.EventSystems;
using UnityEngine.UI;

public class PlayerCard : <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, IBegin<PERSON><PERSON><PERSON><PERSON><PERSON>, IEndDragHandler, IPointerDownHandler, IPointerUpHandler
{
    GameObject selectedOverlay;
    GameObject card;
    GameObject cardPrefab;
    GameObject swapHover;
    ConfigsHandler configsHandler;
    PartyConfigs partyConfigs;

    Camera canvasCamera;
    Transform dragOverlay;
    Transform originalParent;

    PartyCharacters partyCharacters;
    public BattleCharacter character;

    Button cardButton;
    Button detailsButton;
    Button removeButton;
    Button addButton;

    public Vector2 initialPosition;

    public bool isInActive;

    public bool charInStockButAlsoInActive;

    public int partyCharacterIndex;

    private PlayerCard currentTarget = null;
    private bool needsInitialUIUpdate = false;

    // Dictionary that controls the colors of the rarities
    private readonly Dictionary<string, Color> RarityColors = new();

    // Sprite cache for Types
    private readonly Dictionary<Types, Sprite> typeSpriteCache = new();

    //Drag Delay
    public float dragDelay = 0.3f;
    public ScrollRect scrollRect;
    private bool isDragging = false;
    private bool isPointerDown = false;
    private Coroutine delayCoroutine;
    private CanvasGroup canvasGroup;

    private bool isPointerMoving = false;
    private Vector2 pointerDownPos;
    private const float scrollThreshold = 10f; // Pixels

    //Animation
    private Tween wiggleTween;

    // For swapHover
    private PlayerCard previousTarget = null;

    private void Awake()
    {
        canvasGroup = GetComponent<CanvasGroup>();
    }

    // Start is called once before the first execution of Update after the MonoBehaviour is created
    void Start()
    {
        partyConfigs = GameObject.Find("PartiesContainer").GetComponent<PartyConfigs>();
        configsHandler = GameObject.Find("GameConfigsHandler").GetComponent<ConfigsHandler>();
        canvasCamera = GameObject.Find("Configs Camera").GetComponent<Camera>();
        dragOverlay = GameObject.Find("DragOverlay").transform;
        scrollRect = GameObject.Find("PartyConfs").GetComponent<ScrollRect>();

        // Initialize RarityColors dictionary
        RarityColors.Add("Comum", GeneralInfo.GetTierColor("TR2"));
        RarityColors.Add("Inferior", GeneralInfo.GetTierColor("TR5"));
        RarityColors.Add("Raro", GeneralInfo.GetTierColor("TR3"));
        RarityColors.Add("Épico", GeneralInfo.GetTierColor("TR1"));
        RarityColors.Add("Lendário", GeneralInfo.GetTierColor("TR6"));
        RarityColors.Add("Elementar", GeneralInfo.GetTierColor("TR4"));
        RarityColors.Add("Missing", Color.black);

        LoadAllTypeSprites();

        cardPrefab = transform.parent.gameObject;
        selectedOverlay = transform.parent.GetChild(0).gameObject;
        card = gameObject;
        swapHover = transform.parent.GetChild(2).gameObject;

        cardButton = card.GetComponent<Button>();
        detailsButton = selectedOverlay.transform.GetChild(0).GetComponent<Button>();
        removeButton = selectedOverlay.transform.GetChild(1).GetComponent<Button>();
        addButton = selectedOverlay.transform.GetChild(2).GetComponent<Button>();

        detailsButton.onClick.AddListener(() => { if (!isInActive) RemoveFromParty(); else { } });
        removeButton.onClick.AddListener(RemoveFromParty);
        addButton.onClick.AddListener(AddToActiveParty);

        // If Init() was called before Start(), update UI now that components are ready
        if (needsInitialUIUpdate)
        {
            UpdateUI();
            needsInitialUIUpdate = false;
        }
    }

    public void Init(bool isActive, int index, string name)
    {
        isInActive = isActive;
        partyCharacterIndex = index;
        transform.parent.gameObject.name = name;

        if (configsHandler == null) configsHandler = GameObject.Find("GameConfigsHandler").GetComponent<ConfigsHandler>();
        if (partyConfigs == null) partyConfigs = GameObject.Find("PartiesContainer").GetComponent<PartyConfigs>();
        partyCharacters = configsHandler.GetPartyCharacters(partyConfigs.partyIndex);
        if (isInActive) character = partyCharacters.activeCharacters[partyCharacterIndex];
        else character = partyCharacters.stockCharacters[partyCharacterIndex];

        // Mark that we need to update UI after Start() completes
        needsInitialUIUpdate = true;
    }


    // Update is called once per frame
    void Update()
    {
        if (!isInActive && !charInStockButAlsoInActive) // if the character is in the stock changes the details button to remove button
        {
            selectedOverlay.transform.GetChild(0).GetChild(0).GetComponent<TextMeshProUGUI>().text = "Remover";
            selectedOverlay.transform.GetChild(0).GetComponent<Image>().color = new Color(1f, 0f, 0f);

        }
        else if (isInActive || charInStockButAlsoInActive) // if the character is in the active changes the details button to remove button
        {
            selectedOverlay.transform.GetChild(0).GetChild(0).GetComponent<TextMeshProUGUI>().text = "Detalhes";
            selectedOverlay.transform.GetChild(0).GetComponent<Image>().color = new Color(0f, 1f, 0f);
        }
    }

    void SelectCard()
    {
        if (isPointerMoving)
        {
            return;
        }

        DeselectAllCards();

        PlaySelectBounce();

        // Don't allow selection if character is null
        if (character == null)
            return;

        // play the select sound effect
        ConfMenu.PlaySoundWithVolume(GameObject.Find("GameConfigsHandler").GetComponent<AudioSource>(), Resources.Load<AudioClip>("SFX/MenuSound"));

        if (selectedOverlay.activeSelf) selectedOverlay.SetActive(false);
        else selectedOverlay.SetActive(true);

        if (selectedOverlay.activeSelf)
        {
            if (isInActive || charInStockButAlsoInActive)
            {
                selectedOverlay.transform.GetChild(1).gameObject.SetActive(true);
                selectedOverlay.transform.GetChild(2).gameObject.SetActive(false);
            }
            else
            {
                selectedOverlay.transform.GetChild(1).gameObject.SetActive(false);
                selectedOverlay.transform.GetChild(2).gameObject.SetActive(true);
            }
        }

        cardPrefab.transform.SetAsLastSibling();

    }

    void DeselectAllCards()
    {
        foreach (var item in transform.parent.parent.parent.parent.parent.parent.parent.GetComponentsInChildren<CharacterValuesParty>()) // disables all the other selected buttons overlay
        {
            if (item != this) item.HideOverlay();
        }
        foreach (var item in transform.parent.parent.parent.parent.GetComponentsInChildren<PlayerCard>()) // disables all the other selected buttons overlay
        {
            if (item != this) item.selectedOverlay.SetActive(false);
        }

    }

    public void RemoveFromParty()
    {
        if (charInStockButAlsoInActive) // if the character is in the stock but also in the active, remove from both
        {
            foreach (var activeCard in partyConfigs.activePlayers.GetComponentsInChildren<PlayerCard>())
            {
                if (activeCard.character == character)
                {
                    configsHandler.SetCharacterToParty(partyConfigs.partyIndex, activeCard.partyCharacterIndex, true, null);
                    break;
                }
            }

        }

        configsHandler.SetCharacterToParty(partyConfigs.partyIndex, partyCharacterIndex, isInActive, null);

        selectedOverlay.SetActive(false);

        // Update UI for all cards after removing character
        UpdateAllCardsUI();
    }

    public void AddToActiveParty()
    {
        bool addedToParty = false;

        //Check if theres empty slots in the active party
        for (int i = 0; i < partyCharacters.activeCharacters.Length; i++)
        {
            if (partyCharacters.activeCharacters[i] == null)
            {
                configsHandler.SetCharacterToParty(partyConfigs.partyIndex, i, true, character);
                addedToParty = true;
                break;

            }
        }
        if (!addedToParty)
        {
            WarningLabel.Instance.ShowMessage("No empty slots in the active party");
            return; // Stop here — do not continue
        }

        charInStockButAlsoInActive = true;
        selectedOverlay.SetActive(false);

        // Update UI for all cards after adding character to active party
        UpdateAllCardsUI();
    }

    public void UpdateUI()
    {
        // Safety check - don't update UI if components aren't ready yet
        if (card == null || configsHandler == null || partyConfigs == null)
        {
            Debug.LogWarning("Missing essential components for UI update. Skipping.");
            return;
        }

        // 🔧 Always refresh party data to get the most current state
        partyCharacters = configsHandler.GetPartyCharacters(partyConfigs.partyIndex);

        // 🔧 Refresh character reference from current party data
        if (isInActive) character = partyCharacters.activeCharacters[partyCharacterIndex];
        else character = partyCharacters.stockCharacters[partyCharacterIndex];

        // Variable to know if the character is in the stock but also in the active
        charInStockButAlsoInActive = !isInActive && partyCharacters.activeCharacters.FirstOrDefault(c => c == character) != null;

        if (character != null) // if the character isn't null, it show the values
        {
            card.transform.GetChild(0).gameObject.SetActive(false); //EmptySign
            card.transform.GetChild(1).gameObject.SetActive(true); //Player Sprite
            card.transform.GetChild(2).gameObject.SetActive(true); //Rarity
            card.transform.GetChild(3).gameObject.SetActive(true); //Overlay
            card.transform.GetChild(4).gameObject.SetActive(true); //Lv
            card.transform.GetChild(5).gameObject.SetActive(true); //Player Name
            card.transform.GetChild(6).gameObject.SetActive(true); //BigElemDef
            card.transform.GetChild(7).gameObject.SetActive(true); //SmallElemDef
            card.transform.GetChild(8).gameObject.SetActive(true); //BigElemAtk
            card.transform.GetChild(9).gameObject.SetActive(true); //Stars Container
            card.transform.GetChild(10).gameObject.SetActive(!charInStockButAlsoInActive); //Status

            card.transform.GetChild(2).GetComponent<Image>().color = RarityColors[character.rarity];
            card.transform.GetChild(4).GetChild(0).GetComponent<TextMeshProUGUI>().text = character.level.ToString();
            card.transform.GetChild(5).GetComponent<TextMeshProUGUI>().text = character.name;
            Types type1 = character.skills.GetHighestSpDef();
            card.transform.GetChild(6).GetChild(0).GetComponent<Image>().sprite = GetTypeSprite(type1);
            Types type2 = character.skills.GetHighestSpAtk();
            card.transform.GetChild(7).GetChild(0).GetComponent<Image>().sprite = GetTypeSprite(type2);
            Types type3 = character.skills.GetLowestSpDef();
            card.transform.GetChild(8).GetChild(0).GetComponent<Image>().sprite = GetTypeSprite(type3);

            if (character.IsDead)
            {
                card.transform.GetChild(10).gameObject.SetActive(true);
                card.transform.GetChild(10).GetComponent<Image>().color = new Color(1f, 0f, 0f, 0.4f);
                card.transform.GetChild(10).GetChild(0).GetComponent<TextMeshProUGUI>().text = "DEAD";
            }
            else
            {
                card.transform.GetChild(10).gameObject.SetActive(false);
            }

            if (charInStockButAlsoInActive)
            {
                if (character.IsDead)
                {
                    card.transform.GetChild(10).gameObject.SetActive(true);
                    card.transform.GetChild(10).GetComponent<Image>().color = new Color(1f, 0f, 0f, 0.4f);
                    card.transform.GetChild(10).GetChild(0).GetComponent<TextMeshProUGUI>().text = "DEAD";
                }
                else
                {
                    card.transform.GetChild(10).gameObject.SetActive(true);
                    card.transform.GetChild(10).GetComponent<Image>().color = new Color(0f, 1f, 0f, 0.4f);
                    card.transform.GetChild(10).GetChild(0).GetComponent<TextMeshProUGUI>().text = "ACTIVE";
                }
            }
        }
        else
        {
            card.transform.GetChild(0).gameObject.SetActive(true); //EmptySign
            card.transform.GetChild(1).gameObject.SetActive(false); //Player Sprite
            card.transform.GetChild(2).gameObject.SetActive(false); //Rarity
            card.transform.GetChild(3).gameObject.SetActive(false); //Overlay
            card.transform.GetChild(4).gameObject.SetActive(false); //Lv
            card.transform.GetChild(5).gameObject.SetActive(false); //Player Name
            card.transform.GetChild(6).gameObject.SetActive(false); //BigElemDef
            card.transform.GetChild(7).gameObject.SetActive(false); //SmallElemDef
            card.transform.GetChild(8).gameObject.SetActive(false); //BigElemAtk
            card.transform.GetChild(9).gameObject.SetActive(false); //Stars Container
            card.transform.GetChild(10).gameObject.SetActive(false); //Status
        }
    }

    // Update UI for all playerCards in the party
    private void UpdateAllCardsUI()
    {
        // Find all playerCard components in the party container
        PlayerCard[] allCards = partyConfigs.transform.GetComponentsInChildren<PlayerCard>();

        foreach (PlayerCard card in allCards)
        {
            if (card != null)
            {
                card.UpdateUI();
            }
        }
    }



    void LoadAllTypeSprites()
    {
        foreach (Types type in System.Enum.GetValues(typeof(Types)))
        {
            if (!typeSpriteCache.ContainsKey(type))
            {
                Sprite sprite = Resources.Load<Sprite>($"Sprites/BattleEffects/{type}");
                if (sprite != null)
                {
                    typeSpriteCache[type] = sprite;
                }
                else
                {
                    Debug.LogWarning($"Missing sprite for type: {type}");
                }
            }
        }
    }

    Sprite GetTypeSprite(Types type)
    {
        if (typeSpriteCache.TryGetValue(type, out Sprite sprite))
        {
            return sprite;
        }
        else
        {
            Debug.LogWarning($"Sprite not found for type: {type}");
            return null;
        }
    }

    public void OnBeginDrag(PointerEventData eventData)
    {
        // If we're not in drag mode, pass the event to ScrollRect
        if (!isDragging)
        {
            ExecuteEvents.Execute(scrollRect.gameObject, eventData, ExecuteEvents.beginDragHandler);
            return; // Don't do anything else
        }

        // If we are dragging, handle the drag ourselves
        Vector2 cursorPos = canvasCamera.ScreenToWorldPoint(eventData.position);
        cardPrefab.transform.position = cursorPos;

        selectedOverlay.SetActive(false);
    }

    public void OnDrag(PointerEventData eventData)
    {
        if (!isDragging && Vector2.Distance(eventData.position, pointerDownPos) > scrollThreshold)
        isPointerMoving = true;

        if (!isDragging)
        {
            ExecuteEvents.Execute(scrollRect.gameObject, eventData, ExecuteEvents.dragHandler);
            return;
        }

        Vector2 cursorPos = canvasCamera.ScreenToWorldPoint(eventData.position);
        cardPrefab.transform.position = cursorPos;

        // Check what we're currently over during drag
        PlayerCard newTarget = GetCardUnderPointer(eventData.position);

        // Turn off swapHover for previous target if it's different
        if (previousTarget != null && previousTarget != newTarget)
        {
            previousTarget.swapHover.SetActive(false);
        }

        currentTarget = newTarget;
        previousTarget = newTarget;

        selectedOverlay.SetActive(false);

    }

    public void OnEndDrag(PointerEventData eventData)
    {

        if (!isDragging)
        {
            ExecuteEvents.Execute(scrollRect.gameObject, eventData, ExecuteEvents.endDragHandler);
        }

        selectedOverlay.SetActive(false);
    }

    // Adapt to new cardPrefab structure
    private PlayerCard GetCardUnderPointer(Vector2 screenPosition)
    {
        PointerEventData pointerData = new PointerEventData(EventSystem.current)
        {
            position = screenPosition
        };

        List<RaycastResult> results = new List<RaycastResult>();
        EventSystem.current.RaycastAll(pointerData, results);

        foreach (RaycastResult result in results)
        {
            if (result.gameObject == gameObject) continue; // Skip self

            // First check if the hit object has playerCard component in its children
            PlayerCard cards = result.gameObject.GetComponent<PlayerCard>();
            if (cards != null && cards != this)
            {
                cards.swapHover.SetActive(true);
                return cards;
            }
        }

        return null;
    }

    public void OnPointerDown(PointerEventData eventData)
    {
        isPointerDown = true;
        delayCoroutine = StartCoroutine(DragDelayCoroutine(eventData));
    }

    public void OnPointerUp(PointerEventData eventData)
    {
        isPointerDown = false;
        if (delayCoroutine != null) StopCoroutine(delayCoroutine);

        if (isDragging)
        {
            EndDrag(eventData);
            isDragging = false;
            eventData.Use(); // prevent Button onClick
            return;
        }

        // Not dragging and not scrolling — allow selection
        if (!isPointerMoving)
            SelectCard();
    }

    private IEnumerator DragDelayCoroutine(PointerEventData eventData)
    {
        float timer = 0f;
        Vector2 startPos = eventData.position;
        isPointerMoving = false;

        while (timer < dragDelay)
        {
            if (!isPointerDown)
                yield break;

            // Detect scroll gesture
            if (Vector2.Distance(startPos, eventData.position) > 10f)
            {
                isPointerMoving = true;
                yield break; // Stop early — considered scrolling
            }

            timer += Time.unscaledDeltaTime;
            yield return null;
        }

        // If we waited long enough and no scroll → it's a drag
        StartDrag(eventData);
    }

    private void StartDrag(PointerEventData eventData)
    {
        isDragging = true;

        initialPosition = cardPrefab.transform.localPosition;

        originalParent = cardPrefab.transform.parent;

        if (canvasGroup != null)
            canvasGroup.blocksRaycasts = false;

        DeselectAllCards();

        selectedOverlay.SetActive(false);

        cardPrefab.transform.SetParent(dragOverlay);

        StartWiggleAnimation();

    }

    private void EndDrag(PointerEventData eventData)
    {
        // Stop the wiggle animation
        if (wiggleTween != null && wiggleTween.IsActive())
            wiggleTween.Kill();

        cardPrefab.transform.localRotation = Quaternion.identity;

        // Re-enable raycast
        if (canvasGroup != null)
            canvasGroup.blocksRaycasts = true;

        cardPrefab.transform.DOKill(); // Kill any lingering tweens

        // Use the target we tracked during drag
        if (currentTarget != null && currentTarget != this)
        {
            partyConfigs.SwapCharacters(this, currentTarget);

        }
        else
        {
            // Return to parent and original position
            cardPrefab.transform.SetParent(originalParent);
            cardPrefab.transform.DOLocalMove(initialPosition, 0.3f).SetEase(Ease.OutCubic);
        }

        selectedOverlay.SetActive(false);

        if (previousTarget != null)
        {
            previousTarget.swapHover.SetActive(false);
            previousTarget = null;
        }

        // Clear target
        currentTarget = null;
    }

    public void HideOverlay()
    {
        selectedOverlay.SetActive(false);
    }

    private void StartWiggleAnimation()
    {
        if (wiggleTween != null && wiggleTween.IsActive())
            wiggleTween.Kill();

        wiggleTween = cardPrefab.transform
            .DOShakeRotation(duration: 0.2f, strength: new Vector3(0, 1, 1), vibrato: 20, fadeOut: false)
            .SetLoops(-1, LoopType.Restart)
            .SetEase(Ease.Linear);
    }

    private void PlaySelectBounce()
    {

        // Reset any existing tweens on this transform to avoid stacking
        DOTween.Kill("Bounce");

        // Start from default scale
        Vector3 initialScale = cardPrefab.transform.localScale;
        //cardPrefab.transform.localScale = Vector3.one;

        // Apply bounce animation
        cardPrefab.transform
            .DOScale(initialScale * 1.1f, 0.15f) // scale up slightly
            .SetEase(Ease.OutQuad)
            .SetId("Bounce")
            .OnComplete(() =>
            {
                cardPrefab.transform
                    .DOScale(initialScale, 0.15f) // scale back to normal
                    .SetEase(Ease.InQuad);
            });
    }
}
