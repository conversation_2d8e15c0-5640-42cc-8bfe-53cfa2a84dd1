using TMPro;
using UnityEngine.UI;
using UnityEngine;

public class SkillsValueDisplay : MonoBehaviour
{
    
    public GameObject StrengthSpDef, StrengthSpATK, MagicSpDef, MagicSpATK, FireSpDef, FireSpATK, VenomSpDef, VenomSpATK, PossessionSpDef, PossessionSpATK, ElectricitySpDef, ElectricitySpATK, FrostSpDef, FrostSpATK, AcidSpDef, AcidSpATK;

    GameObject RandomButton, PasteButton;

    TMP_InputField StSpDef, StSpATK, MaSpDef, MaSpATK, FiSpDef, FiSpATK, VeSpDef, VeSpATK, PoSpDef, PoSpATK, ElSpDef, ElSpATK, FrSpDef, FrSpATK, AcSpDef, AcSpATK;

    CharConfUI confUI;

    void Start()
    {
        RandomButton = transform.GetChild(0).gameObject;
        PasteButton = GameObject.Find("PasteButton");

        StSpDef = StrengthSpDef.GetComponent<TMP_InputField>();
        StSpATK = StrengthSpATK.GetComponent<TMP_InputField>();

        MaSpDef = MagicSpDef.GetComponent<TMP_InputField>();
        MaSpATK = MagicSpATK.GetComponent<TMP_InputField>();

        FiSpDef = FireSpDef.GetComponent<TMP_InputField>();
        FiSpATK = FireSpATK.GetComponent<TMP_InputField>();

        VeSpDef = VenomSpDef.GetComponent<TMP_InputField>();
        VeSpATK = VenomSpATK.GetComponent<TMP_InputField>();

        PoSpDef = PossessionSpDef.GetComponent<TMP_InputField>();
        PoSpATK = PossessionSpATK.GetComponent<TMP_InputField>();

        ElSpDef = ElectricitySpDef.GetComponent<TMP_InputField>();
        ElSpATK = ElectricitySpATK.GetComponent<TMP_InputField>();

        FrSpDef = FrostSpDef.GetComponent<TMP_InputField>();
        FrSpATK = FrostSpATK.GetComponent<TMP_InputField>();

        AcSpDef = AcidSpDef.GetComponent<TMP_InputField>();
        AcSpATK = AcidSpATK.GetComponent<TMP_InputField>();


        // SpDef inputs
        StSpDef.onEndEdit.AddListener(delegate { confUI.ChangeSPDefAndAtk(Types.Strength, int.Parse(StSpDef.text), int.Parse(StSpATK.text)); });
        MaSpDef.onEndEdit.AddListener(delegate { confUI.ChangeSPDefAndAtk(Types.Magic, int.Parse(MaSpDef.text), int.Parse(MaSpATK.text)); });
        FiSpDef.onEndEdit.AddListener(delegate { confUI.ChangeSPDefAndAtk(Types.Fire, int.Parse(FiSpDef.text), int.Parse(FiSpATK.text)); });
        VeSpDef.onEndEdit.AddListener(delegate { confUI.ChangeSPDefAndAtk(Types.Venom, int.Parse(VeSpDef.text), int.Parse(VeSpATK.text)); });
        PoSpDef.onEndEdit.AddListener(delegate { confUI.ChangeSPDefAndAtk(Types.Possession, int.Parse(PoSpDef.text), int.Parse(PoSpATK.text)); });
        ElSpDef.onEndEdit.AddListener(delegate { confUI.ChangeSPDefAndAtk(Types.Electricity, int.Parse(ElSpDef.text), int.Parse(ElSpATK.text)); });
        FrSpDef.onEndEdit.AddListener(delegate { confUI.ChangeSPDefAndAtk(Types.Frost, int.Parse(FrSpDef.text), int.Parse(FrSpATK.text)); });
        AcSpDef.onEndEdit.AddListener(delegate { confUI.ChangeSPDefAndAtk(Types.Acid, int.Parse(AcSpDef.text), int.Parse(AcSpATK.text)); });

        // SpATK inputs
        StSpATK.onEndEdit.AddListener(delegate { confUI.ChangeSPDefAndAtk(Types.Strength, int.Parse(StSpDef.text), int.Parse(StSpATK.text)); });
        MaSpATK.onEndEdit.AddListener(delegate { confUI.ChangeSPDefAndAtk(Types.Magic, int.Parse(MaSpDef.text), int.Parse(MaSpATK.text)); });
        FiSpATK.onEndEdit.AddListener(delegate { confUI.ChangeSPDefAndAtk(Types.Fire, int.Parse(FiSpDef.text), int.Parse(FiSpATK.text)); });
        VeSpATK.onEndEdit.AddListener(delegate { confUI.ChangeSPDefAndAtk(Types.Venom, int.Parse(VeSpDef.text), int.Parse(VeSpATK.text)); });
        PoSpATK.onEndEdit.AddListener(delegate { confUI.ChangeSPDefAndAtk(Types.Possession, int.Parse(PoSpDef.text), int.Parse(PoSpATK.text)); });
        ElSpATK.onEndEdit.AddListener(delegate { confUI.ChangeSPDefAndAtk(Types.Electricity, int.Parse(ElSpDef.text), int.Parse(ElSpATK.text)); });
        FrSpATK.onEndEdit.AddListener(delegate { confUI.ChangeSPDefAndAtk(Types.Frost, int.Parse(FrSpDef.text), int.Parse(FrSpATK.text)); });
        AcSpATK.onEndEdit.AddListener(delegate { confUI.ChangeSPDefAndAtk(Types.Acid, int.Parse(AcSpDef.text), int.Parse(AcSpATK.text)); });

        // Randomize values
        RandomButton.GetComponent<Button>().onClick.AddListener(delegate { confUI.RandomizeValues(); });

        // Paste values
        PasteButton.GetComponent<Button>().onClick.AddListener(delegate { PasteValues(); });
    }

    void PasteValues()
    {
        if (GameObject.Find("SkillsValues") == null) return;

        string clipboardText = GUIUtility.systemCopyBuffer;
        if (clipboardText.Length > 0)
        {
            Debug.Log(clipboardText);
            string[] values = clipboardText.Split('\n');

            var types = System.Enum.GetValues(typeof(Types));

            if (values[0].Split("\t").Length < 2)
            {
                Debug.Log("Not enough values");
                return;
            }

            if (values[0].Split("\t").Length > 2)
            {
                Debug.Log("Too many values");
                return;
            }

            for (int i = 0; i < values.Length - 1; i++)
            {
                if(i >= types.Length) break;

                string[] stats = values[i].Split('\t');

                confUI.ChangeSPDefAndAtk((Types)i, int.Parse(stats[0]), int.Parse(stats[1]));
            }
        }
    }

    public void SetValues(BattleCharacter character, CharConfUI confUI)
    {
        this.confUI = confUI;

        // it's TMP input field
        StSpDef.text = character.skills.GetValues(Types.Strength).spDef.ToString();
        StSpATK.text = character.skills.GetValues(Types.Strength).spAtk.ToString();

        MaSpDef.text = character.skills.GetValues(Types.Magic).spDef.ToString();
        MaSpATK.text = character.skills.GetValues(Types.Magic).spAtk.ToString();

        FiSpDef.text = character.skills.GetValues(Types.Fire).spDef.ToString();
        FiSpATK.text = character.skills.GetValues(Types.Fire).spAtk.ToString();

        VeSpDef.text = character.skills.GetValues(Types.Venom).spDef.ToString();
        VeSpATK.text = character.skills.GetValues(Types.Venom).spAtk.ToString();

        PoSpDef.text = character.skills.GetValues(Types.Possession).spDef.ToString();
        PoSpATK.text = character.skills.GetValues(Types.Possession).spAtk.ToString();

        ElSpDef.text = character.skills.GetValues(Types.Electricity).spDef.ToString();
        ElSpATK.text = character.skills.GetValues(Types.Electricity).spAtk.ToString();

        FrSpDef.text = character.skills.GetValues(Types.Frost).spDef.ToString();
        FrSpATK.text = character.skills.GetValues(Types.Frost).spAtk.ToString();

        AcSpDef.text = character.skills.GetValues(Types.Acid).spDef.ToString();
        AcSpATK.text = character.skills.GetValues(Types.Acid).spAtk.ToString();
    }
}
