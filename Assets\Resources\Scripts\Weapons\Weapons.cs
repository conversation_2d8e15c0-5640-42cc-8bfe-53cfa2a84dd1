using System.Linq;
using UnityEngine;

public class Weapons
{
    public string id; // Unique identifier
    public string itemId;  // Item id - id of the weapon has an item
    public string name;
    public string description;
    public int wlBase;   // Base WL
    public int qiMin;   // Min Qi
    public int luckMin;   // Min Luck
    public float hc;   // HC
    public bool isSpecial;   // Is special
    public WeaponTags[] weaponTags;   // Tags
    public TargetClasses[] targetClasses;   // Target classes
    public EnabledClasses[] enabledClasses;   // Enabled classes
    public WeaponUpgrade[] weaponUpgrades;   // Weapon upgrades

    // vars based on weapon upgrades
    public int atkw; // AtkW
    public int cooldown; // Cooldown
    public int gold; // Gold
    public int ichor; // Ichor
    public int souls; // Souls
    public int time; // Time
    public int rubies; // Rubies
    public int titanium; // Titanium
    public int adamantium; // Adamantium
    public string rarityName; // Rarity name
    public int shots; // Shots
    public int spdBoost; // Speed boost
    public int goldUp; // Gold upgrade
    public int hellnium; // Hellnium

    public bool isBeingUsed = false;
    public string rarity;
    public int numberOfStars;

    public Weapons(string id, string itemId, string name, string description, int wlBase, int qiMin, int luckMin, float hc, bool isSpecial, WeaponTags[] weaponTags, TargetClasses[] targetClasses, EnabledClasses[] enabledClasses, WeaponUpgrade[] weaponUpgrades)
    {
        this.id = id;
        this.itemId = itemId;
        this.name = name;
        this.description = description;
        this.wlBase = wlBase;
        this.qiMin = qiMin;
        this.luckMin = luckMin;
        this.hc = hc;
        this.isSpecial = isSpecial;
        this.weaponTags = weaponTags;
        this.targetClasses = targetClasses;
        this.enabledClasses = enabledClasses;
        this.weaponUpgrades = weaponUpgrades;
    }

    // Set the rest of the weapon values based on the weapon upgrade
    public void SetWeapon(int wlBase)
    {
        // If the wlBase is lower than the base wlBase, do nothing
        if (wlBase < this.wlBase) return;

        // If the wlBase is higher than 20, do nothing. Weapon max level is 20
        if (wlBase > 20) return;

        // If there is no weapon upgrade for the given wlBase, set rarity and return
        if (weaponUpgrades.FirstOrDefault(x => x.level == wlBase) == null) { SetRarity(); return; }

        // Set the wlBase
        this.wlBase = wlBase;

        // Gets the weapon upgrade based on the wlBase
        atkw = weaponUpgrades.FirstOrDefault(x => x.level == wlBase).atkw;
        cooldown = weaponUpgrades.FirstOrDefault(x => x.level == wlBase).cooldown;
        gold = weaponUpgrades.FirstOrDefault(x => x.level == wlBase).gold;
        ichor = weaponUpgrades.FirstOrDefault(x => x.level == wlBase).ichor;
        souls = weaponUpgrades.FirstOrDefault(x => x.level == wlBase).souls;
        time = weaponUpgrades.FirstOrDefault(x => x.level == wlBase).time;
        rubies = weaponUpgrades.FirstOrDefault(x => x.level == wlBase).rubies;
        titanium = weaponUpgrades.FirstOrDefault(x => x.level == wlBase).titanium;
        adamantium = weaponUpgrades.FirstOrDefault(x => x.level == wlBase).adamantium;
        rarityName = weaponUpgrades.FirstOrDefault(x => x.level == wlBase).rarityName;
        shots = weaponUpgrades.FirstOrDefault(x => x.level == wlBase).shots;
        spdBoost = weaponUpgrades.FirstOrDefault(x => x.level == wlBase).spdBoost;
        goldUp = weaponUpgrades.FirstOrDefault(x => x.level == wlBase).goldUp;
        hellnium = weaponUpgrades.FirstOrDefault(x => x.level == wlBase).hellnium;

        SetRarity();

    }

    void SetRarity()
    {
        if (wlBase >= 1 && wlBase <= 6)
        {
            rarity = "TR2";
            numberOfStars = 2;
        }
        else if (wlBase >= 7 && wlBase <= 12)
        {
            rarity = "TR3";
            numberOfStars = 3;
        }
        else if (wlBase >= 13 && wlBase <= 16)
        {
            rarity = "TR1";
            numberOfStars = 4;
        }
        else if (wlBase >= 17 && wlBase <= 20)
        {
            rarity = "TR6";
            numberOfStars = 5;

        }
        else
        {
            rarity = "Missing";
            numberOfStars = 0;
        }
    }

}

public class WeaponTags
    {
        public string id; // Unique identifier

        public WeaponTags(string id)
        {
            this.id = id;
        }
    }

public class TargetClasses
{
    public string id; // Unique identifier

    public TargetClasses(string id)
    {
        this.id = id;
    }
}

public class EnabledClasses
{
    public string id; // Unique identifier

    public EnabledClasses(string id)
    {
        this.id = id;
    }
}
