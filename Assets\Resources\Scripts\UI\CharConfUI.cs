using TMPro;
using UnityEngine;
using UnityEngine.Events;
using UnityEngine.UI;
using System.Collections.Generic;


public class CharConfUI : MonoBehaviour
{
    public BattleCharacter character; // the character that this UI represents

    private BattleCharacter tempCharacter; // the temporary character to store the values

    Configs<PERSON><PERSON><PERSON> configsHandler; // the Configs<PERSON>andler script

    GameObject enemy, _name, stats, skills, mods, level; // the game objects of the UI

    public GameObject Stats, Skills, Mods; // the game objects of the Stats, Skills and Mods UI

    public GameObject statsFlag, skillsFlag, modsFlag, enemyFlag; // Flags for Change Selected Color

    Button enemyB, statsB, skillsB, modsB; // the buttons of the UI

    TMP_InputField levelI; // the input fields of the UI
    TextMeshProUGUI nameI; // the text of the UI

    [HideInInspector] public string currentActiveTab = null;

    void Start()
    {
        configsHandler = GameObject.Find("GameConfigsHandler").GetComponent<ConfigsHandler>(); // gets the ConfigsHandler script

        // gets the Stats, Skills and Mods UI
        Stats = configsHandler.StatsValues;
        Skills = configsHandler.SkillsValues;
        Mods = configsHandler.ModsValues;

        // gets the game objects of the UI
        enemy = transform.GetChild(0).gameObject;
        _name = transform.GetChild(1).GetChild(0).gameObject;
        stats = transform.GetChild(2).gameObject;
        skills = transform.GetChild(3).gameObject;
        mods = transform.GetChild(4).gameObject;
        level = transform.GetChild(5).gameObject;

        // gets the flags of the UI
        statsFlag = stats.transform.GetChild(0).gameObject;
        skillsFlag = skills.transform.GetChild(0).gameObject;
        modsFlag = mods.transform.GetChild(0).gameObject;
        enemyFlag = enemy.transform.GetChild(0).gameObject;

        // gets the input of the UI
        enemyB = enemy.GetComponent<Button>();
        nameI = _name.GetComponent<TextMeshProUGUI>();
        statsB = stats.GetComponent<Button>();
        skillsB = skills.GetComponent<Button>();
        modsB = mods.GetComponent<Button>();
        levelI = level.GetComponent<TMP_InputField>();

        // adds the listeners to the buttons
        AddButtonListener(enemyB, ChangeCharacterType);
        AddButtonListener(statsB, ValueButtonClick);
        AddButtonListener(skillsB, ValueButtonClick);
        AddButtonListener(modsB, ValueButtonClick);

        // adds the listeners to the input fields
        AddTextEditFinishedListener(levelI, ChangeCharacterlevel);

        nameI.text = character.name;
        levelI.text = character.level.ToString();

    }

    void Update()
    {
        if (character != null) // if the character is not null update the values
        {
            // gets the most updated value of the character
            if (configsHandler.GetCharacter(character) == -1) character = configsHandler.GetCharacterByID(character.id);

            // changes the color of the enemy button
            enemy.transform.GetChild(0).gameObject.SetActive(character.isEnemy);
        }
        else // otherwise destroy the object
        {
            Destroy(gameObject);
        }
    }

    static void AddTextEditFinishedListener(TMP_InputField inputField, UnityAction<string> listener) // adds the listener to the input field when the input field is deselected
    {
        inputField.onEndEdit.AddListener(listener);
    }

    static void AddButtonListener(Button button, UnityAction<Button> listener) // adds the listener to the button
    {
        // play the select sound effect
        ConfMenu.PlaySoundWithVolume(GameObject.Find("GameConfigsHandler").GetComponent<AudioSource>(), Resources.Load<AudioClip>("SFX/MenuSound"));

        button.onClick.AddListener(() => listener(button));
    }

    void ValueButtonClick(Button self)
    {
        // 1. Find the CharConfUI this button belongs to
        CharConfUI charUI = self.GetComponentInParent<CharConfUI>();
        if (charUI == null) return;

        string clickedName = self.gameObject.name;

        // 2. Check if it's the same tab clicked again
        bool isSameTab = clickedName == charUI.currentActiveTab;

        // 3. Deactivate all flags and panels globally
        foreach (var ui in FindObjectsByType<CharConfUI>(FindObjectsSortMode.None))
        {
            if (ui.statsFlag) ui.statsFlag.SetActive(false);
            if (ui.skillsFlag) ui.skillsFlag.SetActive(false);
            if (ui.modsFlag) ui.modsFlag.SetActive(false);
            if (ui.Stats) ui.Stats.SetActive(false);
            if (ui.Skills) ui.Skills.SetActive(false);
            if (ui.Mods) ui.Mods.SetActive(false);

            ui.currentActiveTab = null; // reset all states
        }

        // 4. If it's a different tab or nothing was open, open this one
        if (!isSameTab)
        {
            GameObject panel = null, flag = null;

            switch (clickedName)
            {
                case "Stats": panel = charUI.Stats; flag = charUI.statsFlag; break;
                case "Skills": panel = charUI.Skills; flag = charUI.skillsFlag; break;
                case "Mods": panel = charUI.Mods; flag = charUI.modsFlag; break;
            }

            if (panel != null && flag != null)
            {
                flag.SetActive(true);
                panel.SetActive(true);


                switch (clickedName)
                {
                    case "Stats": panel.GetComponent<StatsValueDisplay>().SetValues(character, charUI); break;
                    case "Skills": panel.GetComponent<SkillsValueDisplay>().SetValues(character, charUI); break;
                    case "Mods": panel.GetComponent<ModsValueDisplay>().SetValues(character, charUI); break;
                }

                charUI.currentActiveTab = clickedName; // update current tab for this prefab
                character.activeTab = clickedName; // update active tab in the character
            }
        }
    }

    public void RandomizeMods()
    {
        if (GameObject.Find("ModsValues") == null) return;

        CharacterMods charmods = configsHandler.defaultMods[Random.Range(0, configsHandler.defaultMods.Count)];

        ChangeMods("", 0, charmods);
    }

    public void RandomizeSpDefAndAtk() // randomizes the values of the character, only used for the special defense and special attack
    {
        if (GameObject.Find("SkillsValues") == null) return;

        CharacterSkills charskills = configsHandler.defaultSkills[Random.Range(0, configsHandler.defaultSkills.Count)];

        ChangeSPDefAndAtk(Types.Acid, 0, 0, charskills);
    }

    public void RandomizeStats()
    {
        if (GameObject.Find("StatsValues") == null) return;

        CharacterStatus charstats = configsHandler.defaultStats[Random.Range(0, configsHandler.defaultStats.Count)];

        List<int> e = new();

        ChangeStats(e, e, e, e, e, charstats);
    }

    public void ChangeStats(List<int> apmin, List<int> hp, List<int> atk, List<int> def, List<int> atkLim, CharacterStatus stats = null) // changes the stats of the character
    {
        // Safety check: ensure we have valid character references
        if (character == null || tempCharacter == null)
        {
            Debug.LogWarning("[CharConfUI] ChangeStats called with null character references. Skipping operation.");
            return;
        }

        if (stats != null) tempCharacter.stats = stats;
        else
        {
            // sets the values of the temporary character
            tempCharacter.stats.UpdateApMin(apmin);
            tempCharacter.stats.UpdateBl();
            tempCharacter.stats.UpdateHp(hp);
            tempCharacter.stats.UpdateAtk(atk);
            tempCharacter.stats.UpdateDef(def);
            tempCharacter.stats.UpdateAtkLim(atkLim);
        }

        // updates the level to reset it's hp and max hp
        tempCharacter.SetLevel(tempCharacter.level);

        // gets the index of the character in the list and sets the character to the temporary character
        int index = configsHandler.GetCharacter(character);
        if (index == -1)
        {
            Debug.LogWarning("[CharConfUI] Character not found in configs handler. Cannot save changes.");
            return;
        }

        configsHandler.SetCharacter(tempCharacter, index);
        character = tempCharacter;

        // after changing the values, updates the Stats UI
        Stats.GetComponent<StatsValueDisplay>().SetValues(character, this);
    }

    public void ChangeSPDefAndAtk(Types type, string spDef, string spAtk, CharacterSkills skills = null) // changes the special defense and special attack of the character
    {
        // Safety check: ensure we have valid character references
        if (character == null || tempCharacter == null)
        {
            Debug.LogWarning("[CharConfUI] ChangeSPDefAndAtk called with null character references. Skipping operation.");
            return;
        }

        if (skills != null) tempCharacter.skills = skills;
        else
        {
            string typeName = type.ToString();
            string acronym = typeName.Length >= 2 ? typeName[..2] : typeName;
            tempCharacter.skills.SetValues(type, new SkillValues(spDef ?? "0", spAtk ?? "0", typeName, acronym)); // sets the values of the temporary character
        }

        int index = configsHandler.GetCharacter(character); // gets the index of the character in the list
        if (index == -1)
        {
            Debug.LogWarning("[CharConfUI] Character not found in configs handler. Cannot save changes.");
            return;
        }

        configsHandler.SetCharacter(tempCharacter, index); // sets the character to the temporary character
        character = tempCharacter;

        ValueButtonClick(skillsB); // updates the Skills UI
    }

    // Overload method for backward compatibility with int parameters
    public void ChangeSPDefAndAtk(Types type, int spDef, int spAtk, CharacterSkills skills = null)
    {
        ChangeSPDefAndAtk(type, spDef.ToString(), spAtk.ToString(), skills);
    }

    public void ChangeMods(string modName, int value, CharacterMods mods = null) // changes the mods of the character
    {
        // Safety check: ensure we have valid character references
        if (character == null || tempCharacter == null)
        {
            Debug.LogWarning("[CharConfUI] ChangeMods called with null character references. Skipping operation.");
            return;
        }

        if (mods != null) tempCharacter.mods = mods;
        else
            switch (modName.ToLower()) // sets the value of the temporary character
            {
                case "knowledge":
                    tempCharacter.mods.SetKnowledge(value);
                    break;
                case "luck":
                    tempCharacter.mods.SetLuck(value);
                    break;
                case "speed":
                    tempCharacter.mods.SetSpeed(value);
                    break;
                case "precision":
                    tempCharacter.mods.SetPrecision(value);
                    break;
                case "evasion":
                    tempCharacter.mods.SetEvasion(value);
                    break;
                case "criticalchance":
                    tempCharacter.mods.SetCriticalChance(value);
                    break;
                case "parryChance":
                    tempCharacter.mods.SetParryChance(value);
                    break;

            }

        int index = configsHandler.GetCharacter(character); // gets the index of the character in the list
        if (index == -1)
        {
            Debug.LogWarning("[CharConfUI] Character not found in configs handler. Cannot save changes.");
            return;
        }

        configsHandler.SetCharacter(tempCharacter, index); // sets the character to the temporary character
        character = tempCharacter;

        ValueButtonClick(modsB); // updates the Mods UI
    }

    void ChangeCharacterType(Button self) // changes the character type
    {
        // Safety check: ensure we have valid character references
        if (character == null || tempCharacter == null)
        {
            Debug.LogWarning("[CharConfUI] ChangeCharacterType called with null character references. Skipping operation.");
            return;
        }

        tempCharacter.isEnemy = !tempCharacter.isEnemy; // inverts the enemy type of the temporary character
        int index = configsHandler.GetCharacter(character); // gets the index of the character in the list
        if (index == -1)
        {
            Debug.LogWarning("[CharConfUI] Character not found in configs handler. Cannot save changes.");
            return;
        }

        configsHandler.SetCharacter(tempCharacter, index); // sets the character to the temporary character
        character = tempCharacter;

        if (character.isEnemy) StartCoroutine(configsHandler.SetEnemyCharacter(character)); // if the character is an enemy, sets the enemy character
    }

    void RemoveCharacter(Button self) // removes the character
    {
        configsHandler.RemoveCharacter(character); // removes the character from the list
        character = null; // sets the character to null

        // sets the Stats, Skills and Mods UI to deactivated
        if (Stats != null) Stats.SetActive(false);

        if (Skills != null) Skills.SetActive(false);

        if (Mods != null) Mods.SetActive(false);
    }

    void ChangeCharacterName(string arg) // changes the character name
    {
        // Safety check: ensure we have valid character references
        if (character == null || tempCharacter == null)
        {
            Debug.LogWarning("[CharConfUI] ChangeCharacterName called with null character references. Skipping operation.");
            return;
        }

        tempCharacter.name = nameI.text; // sets the name of the temporary character
        int index = configsHandler.GetCharacter(character); // gets the index of the character in the list
        if (index == -1)
        {
            Debug.LogWarning("[CharConfUI] Character not found in configs handler. Cannot save changes.");
            return;
        }

        configsHandler.SetCharacter(tempCharacter, index); // sets the character to the temporary character

        character = tempCharacter;
    }

    void ChangeCharacterlevel(string arg) // changes the character level
    {
        // Safety check: ensure we have valid character references
        if (character == null || tempCharacter == null)
        {
            Debug.LogWarning("[CharConfUI] ChangeCharacterlevel called with null character references. Skipping operation.");
            return;
        }

        if (int.Parse(levelI.text) < tempCharacter.baseLevel)
        {
            levelI.text = tempCharacter.baseLevel.ToString();
            WarningLabel.Instance.ShowMessage("It's not possible to set level lower than Base Level - " + tempCharacter.baseLevel);
            return;
        }
        else tempCharacter.SetLevel(int.Parse(levelI.text)); // sets the level of the temporary character

        int index = configsHandler.GetCharacter(character); // gets the index of the character in the list
        if (index == -1)
        {
            Debug.LogWarning("[CharConfUI] Character not found in configs handler. Cannot save changes.");
            return;
        }

        configsHandler.SetCharacter(tempCharacter, index); // sets the character to the temporary character
        character = tempCharacter;
    }

    public void UpdateUI()
    {
        if (character == null) return;

        // CRITICAL FIX: Update tempCharacter to match the current character
        // This ensures that when UI prefabs are reused, tempCharacter points to the correct character
        tempCharacter = character;

        if (nameI != null) nameI.text = character.name;
        if (levelI != null) levelI.text = character.level.ToString();

        if (character.isEnemy) {
            if (enemyFlag != null) enemyFlag.SetActive(true);
        } else {
            if (enemyFlag != null) enemyFlag.SetActive(false);
        }

        //Always reset first
        if (statsFlag != null) statsFlag.SetActive(false);
        if (skillsFlag != null) skillsFlag.SetActive(false);
        if (modsFlag != null) modsFlag.SetActive(false);

/*         switch (character.activeTab) // checks which tab is active and updates the UI accordingly
        {
            case "Stats":
                if (statsFlag != null) statsFlag.SetActive(true);
                break;
            case "Skills":
                if (skillsFlag != null) skillsFlag.SetActive(true);
                break;
            case "Mods":
                if (modsFlag != null) modsFlag.SetActive(true);
                break;
            default:
                if (statsFlag != null) statsFlag.SetActive(false);
                if (skillsFlag != null) skillsFlag.SetActive(false);
                if (modsFlag != null) modsFlag.SetActive(false);
                break;
        } */
    }

    /// <summary>
    /// Cleans up character references when the UI object is returned to the pool.
    /// This prevents stale references that could cause data to be saved to wrong characters.
    /// </summary>
    public void CleanupForPooling()
    {
        // Clear character references to prevent stale data
        character = null;
        tempCharacter = null;

        // Reset UI state
        currentActiveTab = null;

        // Deactivate all flags
        if (statsFlag != null) statsFlag.SetActive(false);
        if (skillsFlag != null) skillsFlag.SetActive(false);
        if (modsFlag != null) modsFlag.SetActive(false);
        if (enemyFlag != null) enemyFlag.SetActive(false);
    }

}
