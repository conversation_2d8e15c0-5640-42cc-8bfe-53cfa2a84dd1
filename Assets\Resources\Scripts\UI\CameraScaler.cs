using UnityEngine;

public class CameraScaler : MonoBehaviour
{
    private Camera _camera;

    void Start()
    {
        _camera = GetComponent<Camera>();
        UpdateCameraAspect();
    }

    private void Update()
    {
        UpdateCameraAspect();
    }

    void UpdateCameraAspect() // This function is used to update the camera aspect ratio to fit the screen with an aspect ratio of 9:18
    {
        float targetAspect = 9.0f / 18.0f; // calculate the target aspect ratio
        float windowAspect = (float)Screen.width / (float)Screen.height; // calculate the current aspect ratio
        float scaleHeight = windowAspect / targetAspect; // calculate the scale height

        if (scaleHeight < 1.0f) // if the scale height is less than 1 it uses the width as the reference and scales down the height
        {
            Rect rect = _camera.rect;

            rect.width = 1.0f;
            rect.height = scaleHeight;
            rect.x = 0;
            rect.y = (1.0f - scaleHeight) / 2.0f;

            _camera.rect = rect;
        }
        else // else it uses the height as the reference and scales down the width
        {
            float scaleWidth = 1.0f / scaleHeight;

            Rect rect = _camera.rect;

            rect.width = scaleWidth;
            rect.height = 1.0f;
            rect.x = (1.0f - scaleWidth) / 2.0f;
            rect.y = 0;

            _camera.rect = rect;
        }
    }
}

