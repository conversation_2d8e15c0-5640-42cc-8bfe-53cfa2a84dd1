using System;
using System.Collections.Generic;
using System.Linq;
using System.IO;
using TMPro;
using UnityEngine;
using UnityEngine.UI;

public class AIConfs : MonoBehaviour
{
    public float ε = 0.00001f;
    public float β = 2.00000f;
    public float Nt = 3f;
    public float θ = -1f;
    public float κ = 0.5f;
    public int NStochastic = 3;

    public TMP_InputField εInput, βInput, NtInput, θInput, κInput, NeInput;

    public GameObject prefabParent;
    public GameObject columnPrefab;

    GameObject newColumn;

    List<int[]> comboTable = new List<int[]>();
    int[] currentTurn = new int[5]; // 0 = turn, 1–4 = actions

    float μ1, μ2, μ3, μ4;
    float sigma1, sigma2, sigma3, sigma4;
    float λ1, λ2, λ3, λ4;

    public float[] enemyAction1ComboChances = new float[10];
    public float[] enemyAction2ComboChances = new float[10];
    public float[] enemyAction3ComboChances = new float[10];
    public float[] enemyAction4ComboChances = new float[10];

    private LoadValues loadValues;

     private void Awake()
    {
        // Get LoadValues instance and load AI configs from it
        loadValues = GameObject.Find("LoadedValues").GetComponent<LoadValues>();
        LoadAIConfigs();
    }

    // Start is called once before the first execution of Update after the MonoBehaviour is created
    void Start()
    {
        // Initialize UI with current values (loaded in Awake)
        if (εInput != null) εInput.text = ε.ToString();
        if (βInput != null) βInput.text = β.ToString();
        if (NtInput != null) NtInput.text = Nt.ToString();
        if (θInput != null) θInput.text = θ.ToString();
        if (κInput != null) κInput.text = κ.ToString();
        if (NeInput != null) NeInput.text = NStochastic.ToString();

        // Wire listeners that update values and save on change
        if (εInput != null) εInput.onEndEdit.AddListener(_ => { if (float.TryParse(εInput.text, out var v)) { ε = v; SaveAIConfigs(); } else εInput.text = ε.ToString(); });
        if (βInput != null) βInput.onEndEdit.AddListener(_ => { if (float.TryParse(βInput.text, out var v)) { β = v; SaveAIConfigs(); } else βInput.text = β.ToString(); });
        if (NtInput != null) NtInput.onEndEdit.AddListener(_ => { if (float.TryParse(NtInput.text, out var v)) { Nt = v; SaveAIConfigs(); } else NtInput.text = Nt.ToString(); });
        if (θInput != null) θInput.onEndEdit.AddListener(_ => { if (float.TryParse(θInput.text, out var v)) { θ = v; SaveAIConfigs(); } else θInput.text = θ.ToString(); });
        if (κInput != null) κInput.onEndEdit.AddListener(_ => { if (float.TryParse(κInput.text, out var v)) { κ = v; SaveAIConfigs(); } else κInput.text = κ.ToString(); });
        if (NeInput != null) NeInput.onEndEdit.AddListener(_ => { if (int.TryParse(NeInput.text, out var v)) { NStochastic = v; SaveAIConfigs(); } else NeInput.text = NStochastic.ToString(); });
    }

    private void SaveAIConfigs()
    {
        if (loadValues != null)
        {
            loadValues.SaveAIConfigs(ε, β, Nt, θ, κ, NStochastic);
        }
    }

    private void LoadAIConfigs()
    {
        if (loadValues != null)
        {
            ε = loadValues.aiEpsilon;
            β = loadValues.aiBeta;
            Nt = loadValues.aiNt;
            θ = loadValues.aiTheta;
            κ = loadValues.aiKappa;
            NStochastic = loadValues.aiNStochastic;
        }
    }

    // Optional programmatic setters that also persist and update UI
    public void SetEpsilon(float v) { ε = v; if (εInput) εInput.text = ε.ToString(); SaveAIConfigs(); }
    public void SetBeta(float v) { β = v; if (βInput) βInput.text = β.ToString(); SaveAIConfigs(); }
    public void SetNt(float v) { Nt = v; if (NtInput) NtInput.text = Nt.ToString(); SaveAIConfigs(); }
    public void SetTheta(float v) { θ = v; if (θInput) θInput.text = θ.ToString(); SaveAIConfigs(); }
    public void SetKappa(float v) { κ = v; if (κInput) κInput.text = κ.ToString(); SaveAIConfigs(); }
    public void SetNStochastic(int v) { NStochastic = v; if (NeInput) NeInput.text = NStochastic.ToString(); SaveAIConfigs(); }

    // Update is called once per frame
    void Update()
    {

    }

    public void StartNewTurn(int turnNumber)
    {
        currentTurn = new int[5];
        currentTurn[0] = turnNumber; // Set turn number

        // Instantiate a new column prefab and add it to the parent
        newColumn = Instantiate(columnPrefab, prefabParent.transform);
        // Set the turn number text
        newColumn.transform.GetChild(0).GetComponent<TextMeshProUGUI>().text = turnNumber.ToString();
        //Debug.Log("New turn started: " + turnNumber);
    }

    public void AddAction(int actionIndex, int comboCount)
    {
        if (actionIndex < 1 || actionIndex > 4)
        {
            Debug.LogError("Action index must be between 1 and 4");
            return;
        }

        currentTurn[actionIndex] = comboCount;
        // Set the action text
        newColumn.transform.GetChild(actionIndex).GetComponent<TextMeshProUGUI>().text = comboCount.ToString();
        //Debug.Log("Action added: " + actionIndex + " with " + comboCount + " combos");
    }

    public void EndTurn()
    {
        // Make a copy to add to the table
        int[] turnCopy = new int[5];
        currentTurn.CopyTo(turnCopy, 0);
        comboTable.Add(turnCopy);

        // Turns to 0 every action that had no combos
        for (int i = 1; i < 5; i++)
        {
            if (currentTurn[i] == 0)
                newColumn.transform.GetChild(i).GetComponent<TextMeshProUGUI>().text = "0";
        }

        CalculateAvrComboForEachAction(); // No need to pass currentTurn anymore
        StandardDeviation(μ1, μ2, μ3, μ4);
        CalculateLambda(sigma1, sigma2, sigma3, sigma4);
        CalculateEnemyComboChances();
        //Debug.Log("Turn " + currentTurn[0] + " ended with " + currentTurn[1] + ", " + currentTurn[2] + ", " + currentTurn[3] + ", " + currentTurn[4] + " combos");
        //Debug.Log("μ1: " + μ1.ToString("F2") + " μ2: " + μ2.ToString("F2") + " μ3: " + μ3.ToString("F2") + " μ4: " + μ4.ToString("F2"));
        //Debug.Log("sigma1: " + sigma1.ToString("F2") + " sigma2: " + sigma2.ToString("F2") + " sigma3: " + sigma3.ToString("F2") + " sigma4: " + sigma4.ToString("F2"));
        //Debug.Log("λ1: " + λ1.ToString("F2") + " λ2: " + λ2.ToString("F2") + " λ3: " + λ3.ToString("F2") + " λ4: " + λ4.ToString("F2"));

    }


    void CalculateAvrComboForEachAction()
    {
        int totalTurns = comboTable.Count;
        int turnsToAverage = (int)Mathf.Min(Nt, totalTurns); // Only average the most recent Nt turns

        float[] sumPerAction = new float[4]; // For actions 1–4

        // Loop only over the last Nt turns
        for (int t = totalTurns - turnsToAverage; t < totalTurns; t++)
        {
            int[] turn = comboTable[t];
            for (int i = 1; i <= 4; i++)
            {
                sumPerAction[i - 1] += turn[i];
            }
        }

        float[] averages = new float[4];

        for (int i = 0; i < 4; i++)
        {
            averages[i] = (sumPerAction[i] / turnsToAverage) + κ * θ;

            switch (i)
            {
                case 0: μ1 = averages[i]; break;
                case 1: μ2 = averages[i]; break;
                case 2: μ3 = averages[i]; break;
                case 3: μ4 = averages[i]; break;
            }

            //Debug.Log($"μ{i + 1} (Average for Action {i + 1}): {averages[i]:F2} (Sum: {sumPerAction[i]} / {turnsToAverage})");
        }
    }

    void StandardDeviation(float μ1, float μ2, float μ3, float μ4)
    {
        int totalTurns = comboTable.Count;
        int turnsToUse = (int)Mathf.Min(Nt, totalTurns);

        float[] sumOfSquares = new float[4]; // For variance calculation

        for (int t = totalTurns - turnsToUse; t < totalTurns; t++)
        {
            int[] turn = comboTable[t];
            float[] mus = { μ1, μ2, μ3, μ4 };

            for (int i = 1; i <= 4; i++)
            {
                float diff = turn[i] - mus[i - 1];
                sumOfSquares[i - 1] += diff * diff;
            }
        }

        float[] stddevs = new float[4];
        for (int i = 0; i < 4; i++)
        {
            stddevs[i] = Mathf.Sqrt(sumOfSquares[i] / turnsToUse); // Add bias if intended

            switch (i)
            {
                case 0: sigma1 = stddevs[i]; break;
                case 1: sigma2 = stddevs[i]; break;
                case 2: sigma3 = stddevs[i]; break;
                case 3: sigma4 = stddevs[i]; break;
            }

            // Debug.Log($"σ{i + 1} (StdDev for Action {i + 1}): {stddevs[i]:F2}");
        }
    }

    void CalculateLambda(float sigma1, float sigma2, float sigma3, float sigma4)
    {
        λ1 = 1 / (sigma1 + ε) + β;
        λ2 = 1 / (sigma2 + ε) + β;
        λ3 = 1 / (sigma3 + ε) + β;
        λ4 = 1 / (sigma4 + ε) + β;
    }

    /// <summary>
    /// Calculates the normalized probability distributions for enemy actions 1–4
    /// using a Gaussian function based on μ (mean) and λ (sharpness).
    /// </summary>
    void CalculateEnemyComboChances()
    {
        // Store the mean values for each action (μ1 through μ4)
        float[] mus = new float[] { μ1, μ2, μ3, μ4 };

        // Store the lambda values for each action (λ1 through λ4)
        float[] lambdas = new float[] { λ1, λ2, λ3, λ4 };

        // Holds the final normalized probabilities for each action
        List<float[]> allChances = new List<float[]>();

        // Loop over each action (0 to 3 for actions 1 to 4)
        for (int actionIndex = 0; actionIndex < 4; actionIndex++)
        {
            float mu = mus[actionIndex];         // Get current action's μ
            float lambda = lambdas[actionIndex]; // Get current action's λ

            float[] unnormalized = new float[9]; // Stores raw exponential values for combo levels 0–8
            float sum = 0f;                      // Sum of unnormalized values for normalization

            // Calculate unnormalized probability using Gaussian-like function
            for (int i = 0; i <= 8; i++)
            {
                unnormalized[i] = Mathf.Exp(-lambda * Mathf.Pow(i - mu, 2));
                sum += unnormalized[i]; // Accumulate for normalization
            }

            float[] normalized = new float[10]; // Final array: index 0–8 = probabilities, index 9 = total (should be 1)

            // Normalize each value so they sum to 1
            for (int i = 0; i <= 8; i++)
            {
                normalized[i] = unnormalized[i] / sum;
            }

            normalized[9] = 1f; // Theoretical sum of normalized values (useful for sanity check)

            // Add the result to the overall list
            allChances.Add(normalized);

            // Log the results for this action
            //Debug.Log($"Enemy Action {actionIndex + 1} Chances: {string.Join(", ", normalized.Select(p => p.ToString("F4")))}");
        }

        // Assign each action’s result to its corresponding public field
        enemyAction1ComboChances = allChances[0];
        enemyAction2ComboChances = allChances[1];
        enemyAction3ComboChances = allChances[2];
        enemyAction4ComboChances = allChances[3];
    }

    /// <summary>
    /// Selects a random combo index from the top N highest probabilities
    /// in the given enemy action chance array (indexes 0–8).
    /// </summary>
    /// <param name="chances">The enemyAction#ComboChances array</param>
    /// <param name="topCount">How many top values to consider (default is 3)</param>
    /// <returns>The selected combo index (0–8)</returns>
    public int SelectRandomTopComboIndex(float[] chances)
    {
        // Ensure we're only working with indices 0–8 (exclude the final sum at index 9)
        var comboPairs = chances
            .Select((value, index) => new { Index = index, Value = value })
            .Where(p => p.Index <= 8)
            .OrderByDescending(p => p.Value)
            .Take(NStochastic)
            .ToList();

        // Randomly pick one of the top `topCount` values
        int randomIndex = UnityEngine.Random.Range(0, comboPairs.Count);

        // Return the corresponding combo index (0–8)

        // Show changes[]
        //Debug.Log("Chances: " + string.Join(", ", chances.Select(p => p.ToString("F4"))));

        //Debug.Log("Combo index: " + comboPairs[randomIndex].Index);
        return comboPairs[randomIndex].Index;
    }
}
