using UnityEngine;
using UnityEngine.UI;

public class CanvasScaleMatch : MonoBehaviour
{
    CanvasScaler canvas;

    void Start()
    {
        canvas = GetComponent<CanvasScaler>();
    }

    // Update is called once per frame
    void Update() // just changes if the canavas should match the width or the height of the screen
    {
        if (Screen.height * 9 / 18 < Screen.width) canvas.matchWidthOrHeight = 1f;
        else canvas.matchWidthOrHeight = 0f;
    }
}
