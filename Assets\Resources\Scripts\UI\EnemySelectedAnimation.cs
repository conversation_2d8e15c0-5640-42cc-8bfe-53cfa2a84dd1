using UnityEngine;

public class EnemySelectedAnimation : MonoBehaviour
{
    public float speed = 5f, maxScale = 0.5f, minScale = 0.3f;

    float time = 0f;

    // Update is called once per frame
    void Update()
    {
        float amplitude = (maxScale - minScale) / 2f;
        float verticalShift = (maxScale + minScale) / 2f;

        transform.localScale = Vector3.one * ((amplitude * Mathf.Sin(time * speed) + verticalShift) / transform.parent.parent.localScale.x);

        time += Time.deltaTime;
    }
}
