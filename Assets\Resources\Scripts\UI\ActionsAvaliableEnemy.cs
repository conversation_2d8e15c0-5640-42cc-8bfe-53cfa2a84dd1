using TMPro;
using UnityEngine;
using UnityEngine.UI;

public class ActionsAvaliableEnemy : MonoBehaviour
{
    // Reference to the ConfigsHandler
    ConfigsHandler configsHandler;

    Sprite enemyEmptyAction, enemyUsableAction, usedAction;

    public TextMeshProUGUI actionAvaliableEnemy;

    float scaleUp = 1f, scaleDown = 0f;

    bool isPlayerTurn;
    void Start()
    {
        // Get the ConfigsHandler script
        configsHandler = GameObject.Find("GameConfigsHandler").GetComponent<ConfigsHandler>();

        enemyEmptyAction = Resources.Load<Sprite>("Sprites/UI/EnemyEmptyAction");
        enemyUsableAction = Resources.Load<Sprite>("Sprites/UI/EnemyAction");


        usedAction = Resources.Load<Sprite>("Sprites/blank");

        actionAvaliableEnemy.gameObject.transform.GetChild(1).GetComponent<TextMeshProUGUI>().text = KeywordManager.GetWord("ENEMY_TURN");
    }


    void Update()
    {
        // Check if it's the player's turn - if playerTurns and enemyTurns values is equal is always the Player turn
        isPlayerTurn = configsHandler.playerTurn;

        if (!isPlayerTurn)
        {
            // Get the numOfActions from the PLayer or Enemy from ConfigHandler
            int numOfActionsEnemy = configsHandler.enemyActions;

            // Get the numOfEmptyActions from the PLayer or Enemy from ConfigHandler
            int numOfEmptyActionsEnemy = configsHandler.enemyEmptyActions;

            // Update the text with the current actions available
            actionAvaliableEnemy.text = numOfActionsEnemy.ToString();


            for (int i = 0; i < 4; i++)
            {
                Image actionImage = transform.GetChild(i).GetComponent<Image>();

                if (i >= numOfEmptyActionsEnemy && i < numOfActionsEnemy + numOfEmptyActionsEnemy)
                {
                    actionImage.transform.localScale = Vector3.one * Mathf.MoveTowards(actionImage.transform.localScale.x, scaleUp, Time.deltaTime * 2);

                    if (Mathf.Abs(actionImage.transform.localScale.x - scaleUp) < 0.0001f) scaleUp = 1f;
                    if (Mathf.Abs(actionImage.transform.localScale.x - 1.5f) < 0.0001f) scaleUp = 1.5f;
                    if (!isPlayerTurn) actionImage.sprite = enemyUsableAction;
                }
                else if (i < numOfEmptyActionsEnemy && actionImage.transform.localScale.x > 0)
                {
                    scaleDown = i == configsHandler.lostActions && ((Mathf.Abs(scaleDown) < 0.0001f && Mathf.Abs(actionImage.transform.localScale.x - 1f) < 0.0001f) || (actionImage.transform.localScale.x >= 1 && Mathf.Abs(scaleDown) > 0.0001f && Mathf.Abs(actionImage.transform.localScale.x - scaleDown) > 0.0001f)) ? 1.5f : 0f;

                    actionImage.transform.localScale = Vector3.MoveTowards(actionImage.transform.localScale, Vector3.one * scaleDown, Time.deltaTime * 4);
                }
                else if (actionImage.sprite != enemyEmptyAction && i >= numOfEmptyActionsEnemy)
                {
                    if (!isPlayerTurn) actionImage.sprite = enemyEmptyAction;
                    if (actionImage.transform.localScale.x < 1) actionImage.transform.localScale = Vector3.one;
                }
            }
        }
        else
        {
            // Reset the UI to its initial state
            actionAvaliableEnemy.text = "";
            for (int i = 0; i < 4; i++)
            {
                Image actionImage = transform.GetChild(i).GetComponent<Image>();
                actionImage.sprite = enemyEmptyAction;
                actionImage.transform.localScale = Vector3.one;
            }
        }
    }

}
