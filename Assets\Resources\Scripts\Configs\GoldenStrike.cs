using TMPro;
using UnityEngine;

public class GoldenStrike : MonoBehaviour
{
    int index; // the index on the array that difines the combo chance

    Configs<PERSON>and<PERSON> configsHandler; // reference to the configs<PERSON>andler script

    TMP_InputField input; // the input field

    bool isEditting = false; // boolean to check if the input field is being edited


    void Start()
    {
        index = int.Parse(name.Substring(name.Length - 1, 1)); // get the index, this will use the last character of the gameobject name

        configsHandler = GameObject.Find("GameConfigsHandler").GetComponent<ConfigsHandler>(); // get the ConfigsHandler script

        input = GetComponent<TMP_InputField>(); // sets the input

        input.onSelect.AddListener(OnStartTextEdit); // when the input field is selected
        input.onEndEdit.AddListener(OnEndTextEdit); // when the input field is not being edited
    }

    void Update() { if (!isEditting) input.text = configsHandler.GoldenStrike[index].ToString(); } // updates the input field's text to the current combo chance at the index

    private void OnStartTextEdit(string arg) => isEditting = true; // sets the isEditting boolean to true

    private void OnEndTextEdit(string arg) // when the input field is not being edited, it updates the combo chance at the index and sets the isEditting boolean to false
    {
        isEditting = false;
        configsHandler.UpdateGoldenStrike(index, int.Parse(input.text));
    }
}

