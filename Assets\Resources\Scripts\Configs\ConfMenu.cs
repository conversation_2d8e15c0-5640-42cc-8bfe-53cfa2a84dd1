using UnityEngine;
using UnityEngine.UI;

public class ConfMenu : MonoBehaviour
{
    ConfigsHandler configsHandler;
    public GameObject confMenu;
    GameObject audioSection;

    Button cancelButton;
    Button audioButton;
    Button audioCancelButton;

    Toggle musicToggle;
    Toggle sfxToggle;

    Slider volumeSlider;
    Slider musicSlider;
    Slider sfxSlider;

    // ConfMenu is now the central volume controller
    public static float MainVolume { get; private set; } = 1f;
    public static float MusicVolume { get; private set; } = 1f;
    public static float SFXVolume { get; private set; } = 1f;
    public static bool MusicEnabled { get; private set; } = true;
    public static bool SFXEnabled { get; private set; } = true;

    // Store the actual volume levels separately from the toggle states
    private float actualMusicVolume = 1f;
    private float actualSfxVolume = 1f;
    private bool volumeInitialized = false;

    // Reference to audio sources for direct control
    private AudioSource mainAudioSource;
    private AudioSource musicPlayer;

    // Start is called once before the first execution of Update after the MonoBehaviour is created
    void Start()
    {
        configsHandler = GameObject.Find("GameConfigsHandler").GetComponent<ConfigsHandler>();

        // Get references to audio sources for direct volume control
        mainAudioSource = configsHandler.GetComponent<AudioSource>();
        musicPlayer = configsHandler.MusicPlayer;

        cancelButton = confMenu.transform.GetChild(3).GetComponent<Button>();
        cancelButton.onClick.AddListener(CloseConf);

        audioButton = confMenu.transform.GetChild(0).GetComponent<Button>();
        audioButton.onClick.AddListener(OpenAudioConf);

        audioSection = confMenu.transform.GetChild(4).gameObject;

        audioCancelButton = audioSection.transform.GetChild(0).GetComponent<Button>();
        audioCancelButton.onClick.AddListener(CloseAudioConf);

        musicToggle = audioSection.transform.GetChild(2).GetComponent<Toggle>();
        sfxToggle = audioSection.transform.GetChild(3).GetComponent<Toggle>();

        volumeSlider = audioSection.transform.GetChild(4).GetComponent<Slider>();
        musicSlider = audioSection.transform.GetChild(5).GetComponent<Slider>();
        sfxSlider = audioSection.transform.GetChild(6).GetComponent<Slider>();

        // Wait for LoadedValues to finish loading before setting up volume
        if (configsHandler.LoadedValues.didLoad)
        {
            InitializeVolumeSettings();
        }
        else
        {
        }
    }

    private void InitializeVolumeSettings()
    {

        // Load volume values from LoadedValues into static properties
        MainVolume = configsHandler.LoadedValues.mainVolume;
        actualMusicVolume = configsHandler.LoadedValues.musicVolume;
        actualSfxVolume = configsHandler.LoadedValues.sfxVolume;
        MusicEnabled = configsHandler.LoadedValues.musicEnabled;
        SFXEnabled = configsHandler.LoadedValues.sfxEnabled;


        // Set initial UI values WITHOUT triggering events first
        volumeSlider.value = MainVolume;
        musicToggle.isOn = MusicEnabled;
        sfxToggle.isOn = SFXEnabled;
        musicSlider.value = actualMusicVolume;
        sfxSlider.value = actualSfxVolume;

        // Update static volume properties based on toggle states
        UpdateVolumeOutputs();

        // NOW add event listeners AFTER setting initial values
        volumeSlider.onValueChanged.AddListener(ChangeVolume);
        musicSlider.onValueChanged.AddListener(ChangeMusicVolume);
        sfxSlider.onValueChanged.AddListener(ChangeSFXVolume);
        musicToggle.onValueChanged.AddListener(OnMusicToggleChanged);
        sfxToggle.onValueChanged.AddListener(OnSFXToggleChanged);

        volumeInitialized = true;
    }

    private void UpdateVolumeOutputs()
    {
        // Update static properties that other scripts will use
        MusicVolume = MusicEnabled ? actualMusicVolume : 0;
        SFXVolume = SFXEnabled ? actualSfxVolume : 0;

        // Apply volumes directly to audio sources
        if (mainAudioSource != null)
            mainAudioSource.volume = MainVolume;
        if (musicPlayer != null)
            musicPlayer.volume = MusicVolume * MainVolume;
    }

    // Update is called once per frame
    void Update()
    {
        // Initialize volume settings if not done yet and LoadedValues is ready
        if (!volumeInitialized && configsHandler.LoadedValues.didLoad)
        {
            InitializeVolumeSettings();
        }

        // Only sync UI if volume has been initialized
        if (volumeInitialized)
        {
            // Sync UI sliders with internal values (in case they were changed externally)
            if (volumeSlider.value != MainVolume)
                volumeSlider.value = MainVolume;

            if (musicSlider.value != actualMusicVolume)
                musicSlider.value = actualMusicVolume;

            if (sfxSlider.value != actualSfxVolume)
                sfxSlider.value = actualSfxVolume;
        }

        // Don't override toggle states - let user control them manually
        // The toggles control whether audio is enabled/disabled independently of volume level
    }
    public void CloseConf()
    {
        // play the select sound effect
        PlaySoundWithVolume(GameObject.Find("GameConfigsHandler").GetComponent<AudioSource>(), Resources.Load<AudioClip>("SFX/MenuSound"));
        audioSection.SetActive(false);
        confMenu.SetActive(false);
    }

    public void OpenAudioConf()
    {
        // play the select sound effect
        PlaySoundWithVolume(GameObject.Find("GameConfigsHandler").GetComponent<AudioSource>(), Resources.Load<AudioClip>("SFX/MenuSound"));
        audioSection.SetActive(true);
    }

    public void CloseAudioConf()
    {
        // play the select sound effect
        PlaySoundWithVolume(GameObject.Find("GameConfigsHandler").GetComponent<AudioSource>(), Resources.Load<AudioClip>("SFX/MenuSound"));
        audioSection.SetActive(false);
    }

    public void OnMusicToggleChanged(bool isOn)
    {
        // play the select sound effect
        PlaySoundWithVolume(GameObject.Find("GameConfigsHandler").GetComponent<AudioSource>(), Resources.Load<AudioClip>("SFX/MenuSound"));

        MusicEnabled = isOn;
        if (isOn)
        {
            // If turning on and volume is 0, set to a reasonable default
            if (actualMusicVolume <= 0)
                actualMusicVolume = 1f;
        }

        UpdateVolumeOutputs();
        // Save immediately when toggle changes
        StartCoroutine(SaveVolumeSettings());
    }

    public void OnSFXToggleChanged(bool isOn)
    {
        // play the select sound effect
        PlaySoundWithVolume(GameObject.Find("GameConfigsHandler").GetComponent<AudioSource>(), Resources.Load<AudioClip>("SFX/MenuSound"));
        
        SFXEnabled = isOn;
        if (isOn)
        {
            // If turning on and volume is 0, set to a reasonable default
            if (actualSfxVolume <= 0)
                actualSfxVolume = 1f;
        }

        UpdateVolumeOutputs();
        // Save immediately when toggle changes
        StartCoroutine(SaveVolumeSettings());
    }

    public void ChangeVolume(float value)
    {
        MainVolume = value;
        UpdateVolumeOutputs();
        // Save immediately when volume changes
        StartCoroutine(SaveVolumeSettings());
    }

    public void ChangeMusicVolume(float value)
    {
        actualMusicVolume = value;
        UpdateVolumeOutputs();
        // Save immediately when volume changes
        StartCoroutine(SaveVolumeSettings());
    }

    public void ChangeSFXVolume(float value)
    {
        actualSfxVolume = value;
        UpdateVolumeOutputs();
        // Save immediately when volume changes
        StartCoroutine(SaveVolumeSettings());
    }

    private System.Collections.IEnumerator SaveVolumeSettings()
    {
        yield return null; // Wait one frame to ensure all values are updated
        // Save the actual volume levels and toggle states
        configsHandler.LoadedValues.SaveVolumeSettings(MainVolume, actualMusicVolume, actualSfxVolume, MusicEnabled, SFXEnabled);
        configsHandler.LoadedValues.SaveVolumeToFile();
    }

    // Static methods for other scripts to easily access volume values
    public static float GetMainVolume() => MainVolume;
    public static float GetMusicVolume() => MusicVolume;
    public static float GetSFXVolume() => SFXVolume;
    public static bool IsMusicEnabled() => MusicEnabled;
    public static bool IsSFXEnabled() => SFXEnabled;

    // Method for other scripts to play sounds with correct volume
    public static void PlaySoundWithVolume(AudioSource audioSource, AudioClip clip)
    {
        if (audioSource != null && clip != null && SFXEnabled)
        {
            audioSource.volume = SFXVolume * MainVolume;
            audioSource.PlayOneShot(clip);
        }
    }
}
