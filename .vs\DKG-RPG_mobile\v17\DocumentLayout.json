{"Version": 1, "WorkspaceRootPath": "D:\\Menino Autista\\DKG-RPG_mobile\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{17B55E5E-F55B-5408-B623-E429C0DD338F}|Assembly-CSharp.csproj|d:\\menino autista\\dkg-rpg_mobile\\assets\\resources\\scripts\\configs\\configshandler.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{17B55E5E-F55B-5408-B623-E429C0DD338F}|Assembly-CSharp.csproj|solutionrelative:assets\\resources\\scripts\\configs\\configshandler.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{17B55E5E-F55B-5408-B623-E429C0DD338F}|Assembly-CSharp.csproj|D:\\Menino Autista\\DKG-RPG_mobile\\assets\\resources\\scripts\\characters\\battlecharacter.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{17B55E5E-F55B-5408-B623-E429C0DD338F}|Assembly-CSharp.csproj|solutionrelative:assets\\resources\\scripts\\characters\\battlecharacter.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{17B55E5E-F55B-5408-B623-E429C0DD338F}|Assembly-CSharp.csproj|D:\\Menino Autista\\DKG-RPG_mobile\\assets\\resources\\scripts\\minigame\\gridcreator.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{17B55E5E-F55B-5408-B623-E429C0DD338F}|Assembly-CSharp.csproj|solutionrelative:assets\\resources\\scripts\\minigame\\gridcreator.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{17B55E5E-F55B-5408-B623-E429C0DD338F}|Assembly-CSharp.csproj|D:\\Menino Autista\\DKG-RPG_mobile\\assets\\resources\\scripts\\configs\\editstockweight.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{17B55E5E-F55B-5408-B623-E429C0DD338F}|Assembly-CSharp.csproj|solutionrelative:assets\\resources\\scripts\\configs\\editstockweight.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{17B55E5E-F55B-5408-B623-E429C0DD338F}|Assembly-CSharp.csproj|D:\\Menino Autista\\DKG-RPG_mobile\\assets\\resources\\scripts\\tests\\getmeshfaceup.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{17B55E5E-F55B-5408-B623-E429C0DD338F}|Assembly-CSharp.csproj|solutionrelative:assets\\resources\\scripts\\tests\\getmeshfaceup.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{17B55E5E-F55B-5408-B623-E429C0DD338F}|Assembly-CSharp.csproj|D:\\Menino Autista\\DKG-RPG_mobile\\assets\\resources\\scripts\\minigame\\grid.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{17B55E5E-F55B-5408-B623-E429C0DD338F}|Assembly-CSharp.csproj|solutionrelative:assets\\resources\\scripts\\minigame\\grid.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{17B55E5E-F55B-5408-B623-E429C0DD338F}|Assembly-CSharp.csproj|D:\\Menino Autista\\DKG-RPG_mobile\\assets\\resources\\scripts\\minigame\\damagestar.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{17B55E5E-F55B-5408-B623-E429C0DD338F}|Assembly-CSharp.csproj|solutionrelative:assets\\resources\\scripts\\minigame\\damagestar.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{17B55E5E-F55B-5408-B623-E429C0DD338F}|Assembly-CSharp.csproj|D:\\Menino Autista\\DKG-RPG_mobile\\assets\\resources\\scripts\\minigame\\attackeffect.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{17B55E5E-F55B-5408-B623-E429C0DD338F}|Assembly-CSharp.csproj|solutionrelative:assets\\resources\\scripts\\minigame\\attackeffect.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{17B55E5E-F55B-5408-B623-E429C0DD338F}|Assembly-CSharp.csproj|D:\\Menino Autista\\DKG-RPG_mobile\\assets\\resources\\scripts\\fpslocker\\fpscounter.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{17B55E5E-F55B-5408-B623-E429C0DD338F}|Assembly-CSharp.csproj|solutionrelative:assets\\resources\\scripts\\fpslocker\\fpscounter.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{17B55E5E-F55B-5408-B623-E429C0DD338F}|Assembly-CSharp.csproj|D:\\Menino Autista\\DKG-RPG_mobile\\assets\\resources\\scripts\\dicesystem\\rollsleft.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{17B55E5E-F55B-5408-B623-E429C0DD338F}|Assembly-CSharp.csproj|solutionrelative:assets\\resources\\scripts\\dicesystem\\rollsleft.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{17B55E5E-F55B-5408-B623-E429C0DD338F}|Assembly-CSharp.csproj|D:\\Menino Autista\\DKG-RPG_mobile\\assets\\resources\\scripts\\dicesystem\\deathscreen.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{17B55E5E-F55B-5408-B623-E429C0DD338F}|Assembly-CSharp.csproj|solutionrelative:assets\\resources\\scripts\\dicesystem\\deathscreen.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{17B55E5E-F55B-5408-B623-E429C0DD338F}|Assembly-CSharp.csproj|D:\\Menino Autista\\DKG-RPG_mobile\\assets\\resources\\scripts\\dicesystem\\calculatechances.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{17B55E5E-F55B-5408-B623-E429C0DD338F}|Assembly-CSharp.csproj|solutionrelative:assets\\resources\\scripts\\dicesystem\\calculatechances.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{17B55E5E-F55B-5408-B623-E429C0DD338F}|Assembly-CSharp.csproj|D:\\Menino Autista\\DKG-RPG_mobile\\assets\\resources\\scripts\\minigame\\tile.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{17B55E5E-F55B-5408-B623-E429C0DD338F}|Assembly-CSharp.csproj|solutionrelative:assets\\resources\\scripts\\minigame\\tile.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{17B55E5E-F55B-5408-B623-E429C0DD338F}|Assembly-CSharp.csproj|D:\\Menino Autista\\DKG-RPG_mobile\\assets\\resources\\scripts\\configs\\editlettervalues.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{17B55E5E-F55B-5408-B623-E429C0DD338F}|Assembly-CSharp.csproj|solutionrelative:assets\\resources\\scripts\\configs\\editlettervalues.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{17B55E5E-F55B-5408-B623-E429C0DD338F}|Assembly-CSharp.csproj|D:\\Menino Autista\\DKG-RPG_mobile\\assets\\resources\\scripts\\configs\\editmaxpp.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{17B55E5E-F55B-5408-B623-E429C0DD338F}|Assembly-CSharp.csproj|solutionrelative:assets\\resources\\scripts\\configs\\editmaxpp.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{17B55E5E-F55B-5408-B623-E429C0DD338F}|Assembly-CSharp.csproj|D:\\Menino Autista\\DKG-RPG_mobile\\assets\\resources\\scripts\\configs\\skillsvaluedisplay.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{17B55E5E-F55B-5408-B623-E429C0DD338F}|Assembly-CSharp.csproj|solutionrelative:assets\\resources\\scripts\\configs\\skillsvaluedisplay.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{17B55E5E-F55B-5408-B623-E429C0DD338F}|Assembly-CSharp.csproj|D:\\Menino Autista\\DKG-RPG_mobile\\assets\\resources\\scripts\\configs\\buffndebuffs.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{17B55E5E-F55B-5408-B623-E429C0DD338F}|Assembly-CSharp.csproj|solutionrelative:assets\\resources\\scripts\\configs\\buffndebuffs.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{17B55E5E-F55B-5408-B623-E429C0DD338F}|Assembly-CSharp.csproj|D:\\Menino Autista\\DKG-RPG_mobile\\assets\\resources\\scripts\\configs\\editidboffset.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{17B55E5E-F55B-5408-B623-E429C0DD338F}|Assembly-CSharp.csproj|solutionrelative:assets\\resources\\scripts\\configs\\editidboffset.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{17B55E5E-F55B-5408-B623-E429C0DD338F}|Assembly-CSharp.csproj|D:\\Menino Autista\\DKG-RPG_mobile\\assets\\resources\\scripts\\configs\\goldenstrike.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{17B55E5E-F55B-5408-B623-E429C0DD338F}|Assembly-CSharp.csproj|solutionrelative:assets\\resources\\scripts\\configs\\goldenstrike.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{17B55E5E-F55B-5408-B623-E429C0DD338F}|Assembly-CSharp.csproj|D:\\Menino Autista\\DKG-RPG_mobile\\assets\\resources\\scripts\\configs\\changebetweenconfigs.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{17B55E5E-F55B-5408-B623-E429C0DD338F}|Assembly-CSharp.csproj|solutionrelative:assets\\resources\\scripts\\configs\\changebetweenconfigs.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{17B55E5E-F55B-5408-B623-E429C0DD338F}|Assembly-CSharp.csproj|D:\\Menino Autista\\DKG-RPG_mobile\\assets\\resources\\scripts\\configs\\switchcameras.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{17B55E5E-F55B-5408-B623-E429C0DD338F}|Assembly-CSharp.csproj|solutionrelative:assets\\resources\\scripts\\configs\\switchcameras.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{17B55E5E-F55B-5408-B623-E429C0DD338F}|Assembly-CSharp.csproj|D:\\Menino Autista\\DKG-RPG_mobile\\assets\\resources\\scripts\\ui\\energytimepb.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{17B55E5E-F55B-5408-B623-E429C0DD338F}|Assembly-CSharp.csproj|solutionrelative:assets\\resources\\scripts\\ui\\energytimepb.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{17B55E5E-F55B-5408-B623-E429C0DD338F}|Assembly-CSharp.csproj|D:\\Menino Autista\\DKG-RPG_mobile\\assets\\resources\\scripts\\characters\\enemyanimation.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{17B55E5E-F55B-5408-B623-E429C0DD338F}|Assembly-CSharp.csproj|solutionrelative:assets\\resources\\scripts\\characters\\enemyanimation.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{17B55E5E-F55B-5408-B623-E429C0DD338F}|Assembly-CSharp.csproj|D:\\Menino Autista\\DKG-RPG_mobile\\assets\\resources\\scripts\\minigame\\restartgame.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{17B55E5E-F55B-5408-B623-E429C0DD338F}|Assembly-CSharp.csproj|solutionrelative:assets\\resources\\scripts\\minigame\\restartgame.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{17B55E5E-F55B-5408-B623-E429C0DD338F}|Assembly-CSharp.csproj|D:\\Menino Autista\\DKG-RPG_mobile\\assets\\resources\\scripts\\configs\\loadvalues.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{17B55E5E-F55B-5408-B623-E429C0DD338F}|Assembly-CSharp.csproj|solutionrelative:assets\\resources\\scripts\\configs\\loadvalues.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{17B55E5E-F55B-5408-B623-E429C0DD338F}|Assembly-CSharp.csproj|D:\\Menino Autista\\DKG-RPG_mobile\\assets\\resources\\scripts\\ui\\actionmenu.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{17B55E5E-F55B-5408-B623-E429C0DD338F}|Assembly-CSharp.csproj|solutionrelative:assets\\resources\\scripts\\ui\\actionmenu.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{17B55E5E-F55B-5408-B623-E429C0DD338F}|Assembly-CSharp.csproj|D:\\Menino Autista\\DKG-RPG_mobile\\assets\\resources\\scripts\\ui\\playerinterface.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{17B55E5E-F55B-5408-B623-E429C0DD338F}|Assembly-CSharp.csproj|solutionrelative:assets\\resources\\scripts\\ui\\playerinterface.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{17B55E5E-F55B-5408-B623-E429C0DD338F}|Assembly-CSharp.csproj|D:\\Menino Autista\\DKG-RPG_mobile\\assets\\resources\\scripts\\ui\\enemyinterface.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{17B55E5E-F55B-5408-B623-E429C0DD338F}|Assembly-CSharp.csproj|solutionrelative:assets\\resources\\scripts\\ui\\enemyinterface.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{17B55E5E-F55B-5408-B623-E429C0DD338F}|Assembly-CSharp.csproj|D:\\Menino Autista\\DKG-RPG_mobile\\assets\\resources\\scripts\\ui\\charconfui.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{17B55E5E-F55B-5408-B623-E429C0DD338F}|Assembly-CSharp.csproj|solutionrelative:assets\\resources\\scripts\\ui\\charconfui.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{17B55E5E-F55B-5408-B623-E429C0DD338F}|Assembly-CSharp.csproj|D:\\Menino Autista\\DKG-RPG_mobile\\assets\\resources\\scripts\\configs\\switchturnsanimation.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{17B55E5E-F55B-5408-B623-E429C0DD338F}|Assembly-CSharp.csproj|solutionrelative:assets\\resources\\scripts\\configs\\switchturnsanimation.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{17B55E5E-F55B-5408-B623-E429C0DD338F}|Assembly-CSharp.csproj|D:\\Menino Autista\\DKG-RPG_mobile\\assets\\resources\\scripts\\configs\\partyconfigs.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{17B55E5E-F55B-5408-B623-E429C0DD338F}|Assembly-CSharp.csproj|solutionrelative:assets\\resources\\scripts\\configs\\partyconfigs.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{17B55E5E-F55B-5408-B623-E429C0DD338F}|Assembly-CSharp.csproj|D:\\Menino Autista\\DKG-RPG_mobile\\assets\\resources\\scripts\\configs\\modsvaluedisplay.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{17B55E5E-F55B-5408-B623-E429C0DD338F}|Assembly-CSharp.csproj|solutionrelative:assets\\resources\\scripts\\configs\\modsvaluedisplay.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{17B55E5E-F55B-5408-B623-E429C0DD338F}|Assembly-CSharp.csproj|D:\\Menino Autista\\DKG-RPG_mobile\\assets\\resources\\scripts\\minigame\\tiledestroy.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{17B55E5E-F55B-5408-B623-E429C0DD338F}|Assembly-CSharp.csproj|solutionrelative:assets\\resources\\scripts\\minigame\\tiledestroy.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{17B55E5E-F55B-5408-B623-E429C0DD338F}|Assembly-CSharp.csproj|D:\\Menino Autista\\DKG-RPG_mobile\\assets\\resources\\scripts\\minigame\\tilecollisionhandler.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{17B55E5E-F55B-5408-B623-E429C0DD338F}|Assembly-CSharp.csproj|solutionrelative:assets\\resources\\scripts\\minigame\\tilecollisionhandler.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{17B55E5E-F55B-5408-B623-E429C0DD338F}|Assembly-CSharp.csproj|D:\\Menino Autista\\DKG-RPG_mobile\\assets\\resources\\scripts\\sfx\\randomizemusic.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{17B55E5E-F55B-5408-B623-E429C0DD338F}|Assembly-CSharp.csproj|solutionrelative:assets\\resources\\scripts\\sfx\\randomizemusic.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{17B55E5E-F55B-5408-B623-E429C0DD338F}|Assembly-CSharp.csproj|D:\\Menino Autista\\DKG-RPG_mobile\\assets\\resources\\scripts\\configs\\combochance.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{17B55E5E-F55B-5408-B623-E429C0DD338F}|Assembly-CSharp.csproj|solutionrelative:assets\\resources\\scripts\\configs\\combochance.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{17B55E5E-F55B-5408-B623-E429C0DD338F}|Assembly-CSharp.csproj|D:\\Menino Autista\\DKG-RPG_mobile\\assets\\resources\\scripts\\configs\\addcharbutton.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{17B55E5E-F55B-5408-B623-E429C0DD338F}|Assembly-CSharp.csproj|solutionrelative:assets\\resources\\scripts\\configs\\addcharbutton.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{17B55E5E-F55B-5408-B623-E429C0DD338F}|Assembly-CSharp.csproj|D:\\Menino Autista\\DKG-RPG_mobile\\assets\\resources\\scripts\\configs\\addbuffbutton.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{17B55E5E-F55B-5408-B623-E429C0DD338F}|Assembly-CSharp.csproj|solutionrelative:assets\\resources\\scripts\\configs\\addbuffbutton.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{17B55E5E-F55B-5408-B623-E429C0DD338F}|Assembly-CSharp.csproj|D:\\Menino Autista\\DKG-RPG_mobile\\assets\\resources\\scripts\\characters\\characterstatus.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{17B55E5E-F55B-5408-B623-E429C0DD338F}|Assembly-CSharp.csproj|solutionrelative:assets\\resources\\scripts\\characters\\characterstatus.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{17B55E5E-F55B-5408-B623-E429C0DD338F}|Assembly-CSharp.csproj|D:\\Menino Autista\\DKG-RPG_mobile\\assets\\resources\\scripts\\characters\\characterskills.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{17B55E5E-F55B-5408-B623-E429C0DD338F}|Assembly-CSharp.csproj|solutionrelative:assets\\resources\\scripts\\characters\\characterskills.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{17B55E5E-F55B-5408-B623-E429C0DD338F}|Assembly-CSharp.csproj|D:\\Menino Autista\\DKG-RPG_mobile\\assets\\resources\\scripts\\characters\\charactermods.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{17B55E5E-F55B-5408-B623-E429C0DD338F}|Assembly-CSharp.csproj|solutionrelative:assets\\resources\\scripts\\characters\\charactermods.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{17B55E5E-F55B-5408-B623-E429C0DD338F}|Assembly-CSharp.csproj|D:\\Menino Autista\\DKG-RPG_mobile\\assets\\resources\\scripts\\ui\\stockplayersuihandler.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{17B55E5E-F55B-5408-B623-E429C0DD338F}|Assembly-CSharp.csproj|solutionrelative:assets\\resources\\scripts\\ui\\stockplayersuihandler.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{17B55E5E-F55B-5408-B623-E429C0DD338F}|Assembly-CSharp.csproj|D:\\Menino Autista\\DKG-RPG_mobile\\assets\\resources\\scripts\\ui\\charatersforpartyuihandler.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{17B55E5E-F55B-5408-B623-E429C0DD338F}|Assembly-CSharp.csproj|solutionrelative:assets\\resources\\scripts\\ui\\charatersforpartyuihandler.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{17B55E5E-F55B-5408-B623-E429C0DD338F}|Assembly-CSharp.csproj|D:\\Menino Autista\\DKG-RPG_mobile\\assets\\resources\\scripts\\types.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{17B55E5E-F55B-5408-B623-E429C0DD338F}|Assembly-CSharp.csproj|solutionrelative:assets\\resources\\scripts\\types.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{17B55E5E-F55B-5408-B623-E429C0DD338F}|Assembly-CSharp.csproj|D:\\Menino Autista\\DKG-RPG_mobile\\assets\\resources\\scripts\\ui\\turnsui.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{17B55E5E-F55B-5408-B623-E429C0DD338F}|Assembly-CSharp.csproj|solutionrelative:assets\\resources\\scripts\\ui\\turnsui.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{17B55E5E-F55B-5408-B623-E429C0DD338F}|Assembly-CSharp.csproj|D:\\Menino Autista\\DKG-RPG_mobile\\assets\\resources\\scripts\\ui\\skilluihandler.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{17B55E5E-F55B-5408-B623-E429C0DD338F}|Assembly-CSharp.csproj|solutionrelative:assets\\resources\\scripts\\ui\\skilluihandler.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{17B55E5E-F55B-5408-B623-E429C0DD338F}|Assembly-CSharp.csproj|D:\\Menino Autista\\DKG-RPG_mobile\\assets\\resources\\scripts\\ui\\skilliconupdate.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{17B55E5E-F55B-5408-B623-E429C0DD338F}|Assembly-CSharp.csproj|solutionrelative:assets\\resources\\scripts\\ui\\skilliconupdate.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{17B55E5E-F55B-5408-B623-E429C0DD338F}|Assembly-CSharp.csproj|D:\\Menino Autista\\DKG-RPG_mobile\\assets\\resources\\scripts\\ui\\score.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{17B55E5E-F55B-5408-B623-E429C0DD338F}|Assembly-CSharp.csproj|solutionrelative:assets\\resources\\scripts\\ui\\score.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{17B55E5E-F55B-5408-B623-E429C0DD338F}|Assembly-CSharp.csproj|D:\\Menino Autista\\DKG-RPG_mobile\\assets\\resources\\scripts\\ui\\restartbutton.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{17B55E5E-F55B-5408-B623-E429C0DD338F}|Assembly-CSharp.csproj|solutionrelative:assets\\resources\\scripts\\ui\\restartbutton.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{17B55E5E-F55B-5408-B623-E429C0DD338F}|Assembly-CSharp.csproj|D:\\Menino Autista\\DKG-RPG_mobile\\assets\\resources\\scripts\\ui\\randomizebackground.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{17B55E5E-F55B-5408-B623-E429C0DD338F}|Assembly-CSharp.csproj|solutionrelative:assets\\resources\\scripts\\ui\\randomizebackground.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{17B55E5E-F55B-5408-B623-E429C0DD338F}|Assembly-CSharp.csproj|D:\\Menino Autista\\DKG-RPG_mobile\\assets\\resources\\scripts\\ui\\inverteside.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{17B55E5E-F55B-5408-B623-E429C0DD338F}|Assembly-CSharp.csproj|solutionrelative:assets\\resources\\scripts\\ui\\inverteside.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{17B55E5E-F55B-5408-B623-E429C0DD338F}|Assembly-CSharp.csproj|D:\\Menino Autista\\DKG-RPG_mobile\\assets\\resources\\scripts\\ui\\escapepopup.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{17B55E5E-F55B-5408-B623-E429C0DD338F}|Assembly-CSharp.csproj|solutionrelative:assets\\resources\\scripts\\ui\\escapepopup.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{17B55E5E-F55B-5408-B623-E429C0DD338F}|Assembly-CSharp.csproj|D:\\Menino Autista\\DKG-RPG_mobile\\assets\\resources\\scripts\\ui\\escapefailpopup.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{17B55E5E-F55B-5408-B623-E429C0DD338F}|Assembly-CSharp.csproj|solutionrelative:assets\\resources\\scripts\\ui\\escapefailpopup.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{17B55E5E-F55B-5408-B623-E429C0DD338F}|Assembly-CSharp.csproj|D:\\Menino Autista\\DKG-RPG_mobile\\assets\\resources\\scripts\\ui\\enemyvalues.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{17B55E5E-F55B-5408-B623-E429C0DD338F}|Assembly-CSharp.csproj|solutionrelative:assets\\resources\\scripts\\ui\\enemyvalues.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{17B55E5E-F55B-5408-B623-E429C0DD338F}|Assembly-CSharp.csproj|D:\\Menino Autista\\DKG-RPG_mobile\\assets\\resources\\scripts\\ui\\enemyselectedanimation.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{17B55E5E-F55B-5408-B623-E429C0DD338F}|Assembly-CSharp.csproj|solutionrelative:assets\\resources\\scripts\\ui\\enemyselectedanimation.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{17B55E5E-F55B-5408-B623-E429C0DD338F}|Assembly-CSharp.csproj|D:\\Menino Autista\\DKG-RPG_mobile\\assets\\resources\\scripts\\ui\\damagelabel.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{17B55E5E-F55B-5408-B623-E429C0DD338F}|Assembly-CSharp.csproj|solutionrelative:assets\\resources\\scripts\\ui\\damagelabel.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{17B55E5E-F55B-5408-B623-E429C0DD338F}|Assembly-CSharp.csproj|D:\\Menino Autista\\DKG-RPG_mobile\\assets\\resources\\scripts\\ui\\charactervaluesparty.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{17B55E5E-F55B-5408-B623-E429C0DD338F}|Assembly-CSharp.csproj|solutionrelative:assets\\resources\\scripts\\ui\\charactervaluesparty.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{17B55E5E-F55B-5408-B623-E429C0DD338F}|Assembly-CSharp.csproj|D:\\Menino Autista\\DKG-RPG_mobile\\assets\\resources\\scripts\\ui\\canvasscalematch.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{17B55E5E-F55B-5408-B623-E429C0DD338F}|Assembly-CSharp.csproj|solutionrelative:assets\\resources\\scripts\\ui\\canvasscalematch.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{17B55E5E-F55B-5408-B623-E429C0DD338F}|Assembly-CSharp.csproj|D:\\Menino Autista\\DKG-RPG_mobile\\assets\\resources\\scripts\\ui\\camerascaler.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{17B55E5E-F55B-5408-B623-E429C0DD338F}|Assembly-CSharp.csproj|solutionrelative:assets\\resources\\scripts\\ui\\camerascaler.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{17B55E5E-F55B-5408-B623-E429C0DD338F}|Assembly-CSharp.csproj|D:\\Menino Autista\\DKG-RPG_mobile\\assets\\resources\\scripts\\ui\\buffconfui.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{17B55E5E-F55B-5408-B623-E429C0DD338F}|Assembly-CSharp.csproj|solutionrelative:assets\\resources\\scripts\\ui\\buffconfui.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{17B55E5E-F55B-5408-B623-E429C0DD338F}|Assembly-CSharp.csproj|D:\\Menino Autista\\DKG-RPG_mobile\\assets\\resources\\scripts\\ui\\addcharacter.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{17B55E5E-F55B-5408-B623-E429C0DD338F}|Assembly-CSharp.csproj|solutionrelative:assets\\resources\\scripts\\ui\\addcharacter.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{17B55E5E-F55B-5408-B623-E429C0DD338F}|Assembly-CSharp.csproj|D:\\Menino Autista\\DKG-RPG_mobile\\assets\\resources\\scripts\\ui\\addbuffndebuff.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{17B55E5E-F55B-5408-B623-E429C0DD338F}|Assembly-CSharp.csproj|solutionrelative:assets\\resources\\scripts\\ui\\addbuffndebuff.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{17B55E5E-F55B-5408-B623-E429C0DD338F}|Assembly-CSharp.csproj|D:\\Menino Autista\\DKG-RPG_mobile\\assets\\resources\\scripts\\ui\\actionsavalable.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{17B55E5E-F55B-5408-B623-E429C0DD338F}|Assembly-CSharp.csproj|solutionrelative:assets\\resources\\scripts\\ui\\actionsavalable.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{17B55E5E-F55B-5408-B623-E429C0DD338F}|Assembly-CSharp.csproj|D:\\Menino Autista\\DKG-RPG_mobile\\assets\\resources\\scripts\\tests\\touch tests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{17B55E5E-F55B-5408-B623-E429C0DD338F}|Assembly-CSharp.csproj|solutionrelative:assets\\resources\\scripts\\tests\\touch tests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{17B55E5E-F55B-5408-B623-E429C0DD338F}|Assembly-CSharp.csproj|D:\\Menino Autista\\DKG-RPG_mobile\\assets\\resources\\scripts\\tests\\stuneffect.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{17B55E5E-F55B-5408-B623-E429C0DD338F}|Assembly-CSharp.csproj|solutionrelative:assets\\resources\\scripts\\tests\\stuneffect.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{17B55E5E-F55B-5408-B623-E429C0DD338F}|Assembly-CSharp.csproj|D:\\Menino Autista\\DKG-RPG_mobile\\assets\\resources\\scripts\\tests\\plataformtype.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{17B55E5E-F55B-5408-B623-E429C0DD338F}|Assembly-CSharp.csproj|solutionrelative:assets\\resources\\scripts\\tests\\plataformtype.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{17B55E5E-F55B-5408-B623-E429C0DD338F}|Assembly-CSharp.csproj|D:\\Menino Autista\\DKG-RPG_mobile\\assets\\resources\\scripts\\tests\\importfile.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{17B55E5E-F55B-5408-B623-E429C0DD338F}|Assembly-CSharp.csproj|solutionrelative:assets\\resources\\scripts\\tests\\importfile.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{17B55E5E-F55B-5408-B623-E429C0DD338F}|Assembly-CSharp.csproj|D:\\Menino Autista\\DKG-RPG_mobile\\assets\\resources\\scripts\\tests\\loadingicon.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{17B55E5E-F55B-5408-B623-E429C0DD338F}|Assembly-CSharp.csproj|solutionrelative:assets\\resources\\scripts\\tests\\loadingicon.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{17B55E5E-F55B-5408-B623-E429C0DD338F}|Assembly-CSharp.csproj|D:\\Menino Autista\\DKG-RPG_mobile\\assets\\resources\\scripts\\fpslocker\\fpslocker.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{17B55E5E-F55B-5408-B623-E429C0DD338F}|Assembly-CSharp.csproj|solutionrelative:assets\\resources\\scripts\\fpslocker\\fpslocker.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{17B55E5E-F55B-5408-B623-E429C0DD338F}|Assembly-CSharp.csproj|D:\\Menino Autista\\DKG-RPG_mobile\\assets\\resources\\scripts\\configs\\valuesscroll.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{17B55E5E-F55B-5408-B623-E429C0DD338F}|Assembly-CSharp.csproj|solutionrelative:assets\\resources\\scripts\\configs\\valuesscroll.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{17B55E5E-F55B-5408-B623-E429C0DD338F}|Assembly-CSharp.csproj|D:\\Menino Autista\\DKG-RPG_mobile\\assets\\resources\\scripts\\configs\\switchconfigs.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{17B55E5E-F55B-5408-B623-E429C0DD338F}|Assembly-CSharp.csproj|solutionrelative:assets\\resources\\scripts\\configs\\switchconfigs.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{17B55E5E-F55B-5408-B623-E429C0DD338F}|Assembly-CSharp.csproj|D:\\Menino Autista\\DKG-RPG_mobile\\assets\\resources\\scripts\\configs\\statsvaluedisplay.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{17B55E5E-F55B-5408-B623-E429C0DD338F}|Assembly-CSharp.csproj|solutionrelative:assets\\resources\\scripts\\configs\\statsvaluedisplay.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{17B55E5E-F55B-5408-B623-E429C0DD338F}|Assembly-CSharp.csproj|D:\\Menino Autista\\DKG-RPG_mobile\\assets\\resources\\scripts\\configs\\pastebutton.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{17B55E5E-F55B-5408-B623-E429C0DD338F}|Assembly-CSharp.csproj|solutionrelative:assets\\resources\\scripts\\configs\\pastebutton.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{17B55E5E-F55B-5408-B623-E429C0DD338F}|Assembly-CSharp.csproj|D:\\Menino Autista\\DKG-RPG_mobile\\assets\\resources\\scripts\\configs\\partycharacters.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{17B55E5E-F55B-5408-B623-E429C0DD338F}|Assembly-CSharp.csproj|solutionrelative:assets\\resources\\scripts\\configs\\partycharacters.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{17B55E5E-F55B-5408-B623-E429C0DD338F}|Assembly-CSharp.csproj|D:\\Menino Autista\\DKG-RPG_mobile\\assets\\resources\\scripts\\configs\\editreductperpiece.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{17B55E5E-F55B-5408-B623-E429C0DD338F}|Assembly-CSharp.csproj|solutionrelative:assets\\resources\\scripts\\configs\\editreductperpiece.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{17B55E5E-F55B-5408-B623-E429C0DD338F}|Assembly-CSharp.csproj|D:\\Menino Autista\\DKG-RPG_mobile\\assets\\resources\\scripts\\configs\\editfractionperaction.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{17B55E5E-F55B-5408-B623-E429C0DD338F}|Assembly-CSharp.csproj|solutionrelative:assets\\resources\\scripts\\configs\\editfractionperaction.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{17B55E5E-F55B-5408-B623-E429C0DD338F}|Assembly-CSharp.csproj|D:\\Menino Autista\\DKG-RPG_mobile\\assets\\resources\\scripts\\configs\\editfractionmod.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{17B55E5E-F55B-5408-B623-E429C0DD338F}|Assembly-CSharp.csproj|solutionrelative:assets\\resources\\scripts\\configs\\editfractionmod.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{17B55E5E-F55B-5408-B623-E429C0DD338F}|Assembly-CSharp.csproj|D:\\Menino Autista\\DKG-RPG_mobile\\assets\\resources\\scripts\\configs\\editenergytime.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{17B55E5E-F55B-5408-B623-E429C0DD338F}|Assembly-CSharp.csproj|solutionrelative:assets\\resources\\scripts\\configs\\editenergytime.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{17B55E5E-F55B-5408-B623-E429C0DD338F}|Assembly-CSharp.csproj|D:\\Menino Autista\\DKG-RPG_mobile\\assets\\resources\\scripts\\configs\\editdifficulty.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{17B55E5E-F55B-5408-B623-E429C0DD338F}|Assembly-CSharp.csproj|solutionrelative:assets\\resources\\scripts\\configs\\editdifficulty.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}], "DocumentGroupContainers": [{"Orientation": 0, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedWidth": 200, "SelectedChildIndex": 0, "Children": [{"$type": "Document", "DocumentIndex": 0, "Title": "ConfigsHandler.cs", "DocumentMoniker": "D:\\Menino Autista\\DKG-RPG_mobile\\Assets\\Resources\\Scripts\\Configs\\ConfigsHandler.cs", "RelativeDocumentMoniker": "Assets\\Resources\\Scripts\\Configs\\ConfigsHandler.cs", "ToolTip": "D:\\Menino Autista\\DKG-RPG_mobile\\Assets\\Resources\\Scripts\\Configs\\ConfigsHandler.cs", "RelativeToolTip": "Assets\\Resources\\Scripts\\Configs\\ConfigsHandler.cs", "ViewState": "AgIAACgCAAAAAAAAAAAnwFECAAAJAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-02-28T18:57:40.816Z", "IsPinned": true, "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 2, "Title": "GridCreator.cs", "DocumentMoniker": "D:\\Menino Autista\\DKG-RPG_mobile\\Assets\\Resources\\Scripts\\MiniGame\\GridCreator.cs", "RelativeDocumentMoniker": "Assets\\Resources\\Scripts\\MiniGame\\GridCreator.cs", "ToolTip": "D:\\Menino Autista\\DKG-RPG_mobile\\Assets\\Resources\\Scripts\\MiniGame\\GridCreator.cs", "RelativeToolTip": "Assets\\Resources\\Scripts\\MiniGame\\GridCreator.cs", "ViewState": "AgIAANoAAAAAAAAAAAAwwFABAAAsAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-03-11T10:16:26.162Z", "IsPinned": true}, {"$type": "Bookmark", "Name": "ST:0:0:{6324226f-61b6-4f28-92ee-18d4b5fe1e48}"}, {"$type": "Document", "DocumentIndex": 4, "Title": "GetMeshFaceUp.cs", "DocumentMoniker": "D:\\Menino Autista\\DKG-RPG_mobile\\Assets\\Resources\\Scripts\\Tests\\GetMeshFaceUp.cs", "RelativeDocumentMoniker": "Assets\\Resources\\Scripts\\Tests\\GetMeshFaceUp.cs", "ToolTip": "D:\\Menino Autista\\DKG-RPG_mobile\\Assets\\Resources\\Scripts\\Tests\\GetMeshFaceUp.cs", "RelativeToolTip": "Assets\\Resources\\Scripts\\Tests\\GetMeshFaceUp.cs", "ViewState": "AgIAAFMAAAAAAAAAAAAewGkAAAC8AAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-04-03T11:35:34.943Z"}, {"$type": "Document", "DocumentIndex": 43, "Title": "Types.cs", "DocumentMoniker": "D:\\Menino Autista\\DKG-RPG_mobile\\Assets\\Resources\\Scripts\\Types.cs", "RelativeDocumentMoniker": "Assets\\Resources\\Scripts\\Types.cs", "ToolTip": "D:\\Menino Autista\\DKG-RPG_mobile\\Assets\\Resources\\Scripts\\Types.cs", "RelativeToolTip": "Assets\\Resources\\Scripts\\Types.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-04-03T10:52:26.563Z"}, {"$type": "Document", "DocumentIndex": 44, "Title": "TurnsUI.cs", "DocumentMoniker": "D:\\Menino Autista\\DKG-RPG_mobile\\Assets\\Resources\\Scripts\\UI\\TurnsUI.cs", "RelativeDocumentMoniker": "Assets\\Resources\\Scripts\\UI\\TurnsUI.cs", "ToolTip": "D:\\Menino Autista\\DKG-RPG_mobile\\Assets\\Resources\\Scripts\\UI\\TurnsUI.cs", "RelativeToolTip": "Assets\\Resources\\Scripts\\UI\\TurnsUI.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-04-03T10:52:24.518Z"}, {"$type": "Document", "DocumentIndex": 41, "Title": "StockPlayersUIHandler.cs", "DocumentMoniker": "D:\\Menino Autista\\DKG-RPG_mobile\\Assets\\Resources\\Scripts\\UI\\StockPlayersUIHandler.cs", "RelativeDocumentMoniker": "Assets\\Resources\\Scripts\\UI\\StockPlayersUIHandler.cs", "ToolTip": "D:\\Menino Autista\\DKG-RPG_mobile\\Assets\\Resources\\Scripts\\UI\\StockPlayersUIHandler.cs", "RelativeToolTip": "Assets\\Resources\\Scripts\\UI\\StockPlayersUIHandler.cs", "ViewState": "AgIAAB0AAAAAAAAAAAAgwEsAAABNAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-04-03T10:52:23.347Z"}, {"$type": "Document", "DocumentIndex": 45, "Title": "SkillUIHandler.cs", "DocumentMoniker": "D:\\Menino Autista\\DKG-RPG_mobile\\Assets\\Resources\\Scripts\\UI\\SkillUIHandler.cs", "RelativeDocumentMoniker": "Assets\\Resources\\Scripts\\UI\\SkillUIHandler.cs", "ToolTip": "D:\\Menino Autista\\DKG-RPG_mobile\\Assets\\Resources\\Scripts\\UI\\SkillUIHandler.cs", "RelativeToolTip": "Assets\\Resources\\Scripts\\UI\\SkillUIHandler.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-04-03T10:52:22.478Z"}, {"$type": "Document", "DocumentIndex": 46, "Title": "SkillIconUpdate.cs", "DocumentMoniker": "D:\\Menino Autista\\DKG-RPG_mobile\\Assets\\Resources\\Scripts\\UI\\SkillIconUpdate.cs", "RelativeDocumentMoniker": "Assets\\Resources\\Scripts\\UI\\SkillIconUpdate.cs", "ToolTip": "D:\\Menino Autista\\DKG-RPG_mobile\\Assets\\Resources\\Scripts\\UI\\SkillIconUpdate.cs", "RelativeToolTip": "Assets\\Resources\\Scripts\\UI\\SkillIconUpdate.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-04-03T10:52:21.529Z"}, {"$type": "Document", "DocumentIndex": 47, "Title": "Score.cs", "DocumentMoniker": "D:\\Menino Autista\\DKG-RPG_mobile\\Assets\\Resources\\Scripts\\UI\\Score.cs", "RelativeDocumentMoniker": "Assets\\Resources\\Scripts\\UI\\Score.cs", "ToolTip": "D:\\Menino Autista\\DKG-RPG_mobile\\Assets\\Resources\\Scripts\\UI\\Score.cs", "RelativeToolTip": "Assets\\Resources\\Scripts\\UI\\Score.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-04-03T10:52:20.832Z"}, {"$type": "Document", "DocumentIndex": 48, "Title": "RestartButton.cs", "DocumentMoniker": "D:\\Menino Autista\\DKG-RPG_mobile\\Assets\\Resources\\Scripts\\UI\\RestartButton.cs", "RelativeDocumentMoniker": "Assets\\Resources\\Scripts\\UI\\RestartButton.cs", "ToolTip": "D:\\Menino Autista\\DKG-RPG_mobile\\Assets\\Resources\\Scripts\\UI\\RestartButton.cs", "RelativeToolTip": "Assets\\Resources\\Scripts\\UI\\RestartButton.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-04-03T10:52:20.061Z"}, {"$type": "Document", "DocumentIndex": 49, "Title": "RandomizeBackGround.cs", "DocumentMoniker": "D:\\Menino Autista\\DKG-RPG_mobile\\Assets\\Resources\\Scripts\\UI\\RandomizeBackGround.cs", "RelativeDocumentMoniker": "Assets\\Resources\\Scripts\\UI\\RandomizeBackGround.cs", "ToolTip": "D:\\Menino Autista\\DKG-RPG_mobile\\Assets\\Resources\\Scripts\\UI\\RandomizeBackGround.cs", "RelativeToolTip": "Assets\\Resources\\Scripts\\UI\\RandomizeBackGround.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-04-03T10:52:19.334Z"}, {"$type": "Document", "DocumentIndex": 26, "Title": "PlayerInterface.cs", "DocumentMoniker": "D:\\Menino Autista\\DKG-RPG_mobile\\Assets\\Resources\\Scripts\\UI\\PlayerInterface.cs", "RelativeDocumentMoniker": "Assets\\Resources\\Scripts\\UI\\PlayerInterface.cs", "ToolTip": "D:\\Menino Autista\\DKG-RPG_mobile\\Assets\\Resources\\Scripts\\UI\\PlayerInterface.cs", "RelativeToolTip": "Assets\\Resources\\Scripts\\UI\\PlayerInterface.cs", "ViewState": "AgIAABwAAAAAAAAAAAAwwDMAAACGAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-04-03T10:52:18.48Z"}, {"$type": "Document", "DocumentIndex": 50, "Title": "InverteSide.cs", "DocumentMoniker": "D:\\Menino Autista\\DKG-RPG_mobile\\Assets\\Resources\\Scripts\\UI\\InverteSide.cs", "RelativeDocumentMoniker": "Assets\\Resources\\Scripts\\UI\\InverteSide.cs", "ToolTip": "D:\\Menino Autista\\DKG-RPG_mobile\\Assets\\Resources\\Scripts\\UI\\InverteSide.cs", "RelativeToolTip": "Assets\\Resources\\Scripts\\UI\\InverteSide.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-04-03T10:52:17.645Z"}, {"$type": "Document", "DocumentIndex": 51, "Title": "EscapePopup.cs", "DocumentMoniker": "D:\\Menino Autista\\DKG-RPG_mobile\\Assets\\Resources\\Scripts\\UI\\EscapePopup.cs", "RelativeDocumentMoniker": "Assets\\Resources\\Scripts\\UI\\EscapePopup.cs", "ToolTip": "D:\\Menino Autista\\DKG-RPG_mobile\\Assets\\Resources\\Scripts\\UI\\EscapePopup.cs", "RelativeToolTip": "Assets\\Resources\\Scripts\\UI\\EscapePopup.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-04-03T10:52:16.962Z"}, {"$type": "Document", "DocumentIndex": 52, "Title": "EscapeFailPopup.cs", "DocumentMoniker": "D:\\Menino Autista\\DKG-RPG_mobile\\Assets\\Resources\\Scripts\\UI\\EscapeFailPopup.cs", "RelativeDocumentMoniker": "Assets\\Resources\\Scripts\\UI\\EscapeFailPopup.cs", "ToolTip": "D:\\Menino Autista\\DKG-RPG_mobile\\Assets\\Resources\\Scripts\\UI\\EscapeFailPopup.cs", "RelativeToolTip": "Assets\\Resources\\Scripts\\UI\\EscapeFailPopup.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-04-03T10:52:16.221Z"}, {"$type": "Document", "DocumentIndex": 21, "Title": "EnergyTimePB.cs", "DocumentMoniker": "D:\\Menino Autista\\DKG-RPG_mobile\\Assets\\Resources\\Scripts\\UI\\EnergyTimePB.cs", "RelativeDocumentMoniker": "Assets\\Resources\\Scripts\\UI\\EnergyTimePB.cs", "ToolTip": "D:\\Menino Autista\\DKG-RPG_mobile\\Assets\\Resources\\Scripts\\UI\\EnergyTimePB.cs", "RelativeToolTip": "Assets\\Resources\\Scripts\\UI\\EnergyTimePB.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-04-03T10:52:15.41Z"}, {"$type": "Document", "DocumentIndex": 53, "Title": "EnemyValues.cs", "DocumentMoniker": "D:\\Menino Autista\\DKG-RPG_mobile\\Assets\\Resources\\Scripts\\UI\\EnemyValues.cs", "RelativeDocumentMoniker": "Assets\\Resources\\Scripts\\UI\\EnemyValues.cs", "ToolTip": "D:\\Menino Autista\\DKG-RPG_mobile\\Assets\\Resources\\Scripts\\UI\\EnemyValues.cs", "RelativeToolTip": "Assets\\Resources\\Scripts\\UI\\EnemyValues.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-04-03T10:52:13.688Z"}, {"$type": "Document", "DocumentIndex": 54, "Title": "EnemySelectedAnimation.cs", "DocumentMoniker": "D:\\Menino Autista\\DKG-RPG_mobile\\Assets\\Resources\\Scripts\\UI\\EnemySelectedAnimation.cs", "RelativeDocumentMoniker": "Assets\\Resources\\Scripts\\UI\\EnemySelectedAnimation.cs", "ToolTip": "D:\\Menino Autista\\DKG-RPG_mobile\\Assets\\Resources\\Scripts\\UI\\EnemySelectedAnimation.cs", "RelativeToolTip": "Assets\\Resources\\Scripts\\UI\\EnemySelectedAnimation.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-04-03T10:52:13.047Z"}, {"$type": "Document", "DocumentIndex": 27, "Title": "EnemyInterface.cs", "DocumentMoniker": "D:\\Menino Autista\\DKG-RPG_mobile\\Assets\\Resources\\Scripts\\UI\\EnemyInterface.cs", "RelativeDocumentMoniker": "Assets\\Resources\\Scripts\\UI\\EnemyInterface.cs", "ToolTip": "D:\\Menino Autista\\DKG-RPG_mobile\\Assets\\Resources\\Scripts\\UI\\EnemyInterface.cs", "RelativeToolTip": "Assets\\Resources\\Scripts\\UI\\EnemyInterface.cs", "ViewState": "AgIAAEAAAAAAAAAAAAAwwFcAAAAYAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-04-03T10:52:12.169Z"}, {"$type": "Document", "DocumentIndex": 55, "Title": "DamageLabel.cs", "DocumentMoniker": "D:\\Menino Autista\\DKG-RPG_mobile\\Assets\\Resources\\Scripts\\UI\\DamageLabel.cs", "RelativeDocumentMoniker": "Assets\\Resources\\Scripts\\UI\\DamageLabel.cs", "ToolTip": "D:\\Menino Autista\\DKG-RPG_mobile\\Assets\\Resources\\Scripts\\UI\\DamageLabel.cs", "RelativeToolTip": "Assets\\Resources\\Scripts\\UI\\DamageLabel.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-04-03T10:52:11.471Z"}, {"$type": "Document", "DocumentIndex": 28, "Title": "CharConfUI.cs", "DocumentMoniker": "D:\\Menino Autista\\DKG-RPG_mobile\\Assets\\Resources\\Scripts\\UI\\CharConfUI.cs", "RelativeDocumentMoniker": "Assets\\Resources\\Scripts\\UI\\CharConfUI.cs", "ToolTip": "D:\\Menino Autista\\DKG-RPG_mobile\\Assets\\Resources\\Scripts\\UI\\CharConfUI.cs", "RelativeToolTip": "Assets\\Resources\\Scripts\\UI\\CharConfUI.cs", "ViewState": "AgIAANkAAAAAAAAAAAAIwPAAAAAfAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-04-03T10:52:10.655Z"}, {"$type": "Document", "DocumentIndex": 42, "Title": "CharatersForPartyUIHandler.cs", "DocumentMoniker": "D:\\Menino Autista\\DKG-RPG_mobile\\Assets\\Resources\\Scripts\\UI\\CharatersForPartyUIHandler.cs", "RelativeDocumentMoniker": "Assets\\Resources\\Scripts\\UI\\CharatersForPartyUIHandler.cs", "ToolTip": "D:\\Menino Autista\\DKG-RPG_mobile\\Assets\\Resources\\Scripts\\UI\\CharatersForPartyUIHandler.cs", "RelativeToolTip": "Assets\\Resources\\Scripts\\UI\\CharatersForPartyUIHandler.cs", "ViewState": "AgIAABAAAAAAAAAAAAAgwAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-04-03T10:52:09.936Z"}, {"$type": "Document", "DocumentIndex": 56, "Title": "CharacterValuesParty.cs", "DocumentMoniker": "D:\\Menino Autista\\DKG-RPG_mobile\\Assets\\Resources\\Scripts\\UI\\CharacterValuesParty.cs", "RelativeDocumentMoniker": "Assets\\Resources\\Scripts\\UI\\CharacterValuesParty.cs", "ToolTip": "D:\\Menino Autista\\DKG-RPG_mobile\\Assets\\Resources\\Scripts\\UI\\CharacterValuesParty.cs", "RelativeToolTip": "Assets\\Resources\\Scripts\\UI\\CharacterValuesParty.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-04-03T10:52:09.051Z"}, {"$type": "Document", "DocumentIndex": 57, "Title": "CanvasScaleMatch.cs", "DocumentMoniker": "D:\\Menino Autista\\DKG-RPG_mobile\\Assets\\Resources\\Scripts\\UI\\CanvasScaleMatch.cs", "RelativeDocumentMoniker": "Assets\\Resources\\Scripts\\UI\\CanvasScaleMatch.cs", "ToolTip": "D:\\Menino Autista\\DKG-RPG_mobile\\Assets\\Resources\\Scripts\\UI\\CanvasScaleMatch.cs", "RelativeToolTip": "Assets\\Resources\\Scripts\\UI\\CanvasScaleMatch.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-04-03T10:52:05.853Z"}, {"$type": "Document", "DocumentIndex": 58, "Title": "CameraScaler.cs", "DocumentMoniker": "D:\\Menino Autista\\DKG-RPG_mobile\\Assets\\Resources\\Scripts\\UI\\CameraScaler.cs", "RelativeDocumentMoniker": "Assets\\Resources\\Scripts\\UI\\CameraScaler.cs", "ToolTip": "D:\\Menino Autista\\DKG-RPG_mobile\\Assets\\Resources\\Scripts\\UI\\CameraScaler.cs", "RelativeToolTip": "Assets\\Resources\\Scripts\\UI\\CameraScaler.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-04-03T10:52:04.974Z"}, {"$type": "Document", "DocumentIndex": 59, "Title": "BuffConfUI.cs", "DocumentMoniker": "D:\\Menino Autista\\DKG-RPG_mobile\\Assets\\Resources\\Scripts\\UI\\BuffConfUI.cs", "RelativeDocumentMoniker": "Assets\\Resources\\Scripts\\UI\\BuffConfUI.cs", "ToolTip": "D:\\Menino Autista\\DKG-RPG_mobile\\Assets\\Resources\\Scripts\\UI\\BuffConfUI.cs", "RelativeToolTip": "Assets\\Resources\\Scripts\\UI\\BuffConfUI.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-04-03T10:52:03.842Z"}, {"$type": "Document", "DocumentIndex": 60, "Title": "AddCharacter.cs", "DocumentMoniker": "D:\\Menino Autista\\DKG-RPG_mobile\\Assets\\Resources\\Scripts\\UI\\AddCharacter.cs", "RelativeDocumentMoniker": "Assets\\Resources\\Scripts\\UI\\AddCharacter.cs", "ToolTip": "D:\\Menino Autista\\DKG-RPG_mobile\\Assets\\Resources\\Scripts\\UI\\AddCharacter.cs", "RelativeToolTip": "Assets\\Resources\\Scripts\\UI\\AddCharacter.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-04-03T10:52:02.942Z"}, {"$type": "Document", "DocumentIndex": 61, "Title": "AddBuffNDeBuff.cs", "DocumentMoniker": "D:\\Menino Autista\\DKG-RPG_mobile\\Assets\\Resources\\Scripts\\UI\\AddBuffNDeBuff.cs", "RelativeDocumentMoniker": "Assets\\Resources\\Scripts\\UI\\AddBuffNDeBuff.cs", "ToolTip": "D:\\Menino Autista\\DKG-RPG_mobile\\Assets\\Resources\\Scripts\\UI\\AddBuffNDeBuff.cs", "RelativeToolTip": "Assets\\Resources\\Scripts\\UI\\AddBuffNDeBuff.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-04-03T10:52:02.184Z"}, {"$type": "Document", "DocumentIndex": 62, "Title": "ActionsAvalable.cs", "DocumentMoniker": "D:\\Menino Autista\\DKG-RPG_mobile\\Assets\\Resources\\Scripts\\UI\\ActionsAvalable.cs", "RelativeDocumentMoniker": "Assets\\Resources\\Scripts\\UI\\ActionsAvalable.cs", "ToolTip": "D:\\Menino Autista\\DKG-RPG_mobile\\Assets\\Resources\\Scripts\\UI\\ActionsAvalable.cs", "RelativeToolTip": "Assets\\Resources\\Scripts\\UI\\ActionsAvalable.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-04-03T10:52:01.183Z"}, {"$type": "Document", "DocumentIndex": 25, "Title": "ActionMenu.cs", "DocumentMoniker": "D:\\Menino Autista\\DKG-RPG_mobile\\Assets\\Resources\\Scripts\\UI\\ActionMenu.cs", "RelativeDocumentMoniker": "Assets\\Resources\\Scripts\\UI\\ActionMenu.cs", "ToolTip": "D:\\Menino Autista\\DKG-RPG_mobile\\Assets\\Resources\\Scripts\\UI\\ActionMenu.cs", "RelativeToolTip": "Assets\\Resources\\Scripts\\UI\\ActionMenu.cs", "ViewState": "AgIAAHsAAAAAAAAAAAAgwJAAAAAQAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-04-03T10:52:00.36Z"}, {"$type": "Document", "DocumentIndex": 63, "Title": "touch Tests.cs", "DocumentMoniker": "D:\\Menino Autista\\DKG-RPG_mobile\\Assets\\Resources\\Scripts\\Tests\\touch Tests.cs", "RelativeDocumentMoniker": "Assets\\Resources\\Scripts\\Tests\\touch Tests.cs", "ToolTip": "D:\\Menino Autista\\DKG-RPG_mobile\\Assets\\Resources\\Scripts\\Tests\\touch Tests.cs", "RelativeToolTip": "Assets\\Resources\\Scripts\\Tests\\touch Tests.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-04-03T10:51:59.11Z"}, {"$type": "Document", "DocumentIndex": 64, "Title": "StunEffect.cs", "DocumentMoniker": "D:\\Menino Autista\\DKG-RPG_mobile\\Assets\\Resources\\Scripts\\Tests\\StunEffect.cs", "RelativeDocumentMoniker": "Assets\\Resources\\Scripts\\Tests\\StunEffect.cs", "ToolTip": "D:\\Menino Autista\\DKG-RPG_mobile\\Assets\\Resources\\Scripts\\Tests\\StunEffect.cs", "RelativeToolTip": "Assets\\Resources\\Scripts\\Tests\\StunEffect.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-04-03T10:51:58.267Z"}, {"$type": "Document", "DocumentIndex": 65, "Title": "plataformType.cs", "DocumentMoniker": "D:\\Menino Autista\\DKG-RPG_mobile\\Assets\\Resources\\Scripts\\Tests\\plataformType.cs", "RelativeDocumentMoniker": "Assets\\Resources\\Scripts\\Tests\\plataformType.cs", "ToolTip": "D:\\Menino Autista\\DKG-RPG_mobile\\Assets\\Resources\\Scripts\\Tests\\plataformType.cs", "RelativeToolTip": "Assets\\Resources\\Scripts\\Tests\\plataformType.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-04-03T10:51:57.628Z"}, {"$type": "Document", "DocumentIndex": 66, "Title": "ImportFile.cs", "DocumentMoniker": "D:\\Menino Autista\\DKG-RPG_mobile\\Assets\\Resources\\Scripts\\Tests\\ImportFile.cs", "RelativeDocumentMoniker": "Assets\\Resources\\Scripts\\Tests\\ImportFile.cs", "ToolTip": "D:\\Menino Autista\\DKG-RPG_mobile\\Assets\\Resources\\Scripts\\Tests\\ImportFile.cs", "RelativeToolTip": "Assets\\Resources\\Scripts\\Tests\\ImportFile.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-04-03T10:51:56.658Z"}, {"$type": "Document", "DocumentIndex": 67, "Title": "LoadingIcon.cs", "DocumentMoniker": "D:\\Menino Autista\\DKG-RPG_mobile\\Assets\\Resources\\Scripts\\Tests\\LoadingIcon.cs", "RelativeDocumentMoniker": "Assets\\Resources\\Scripts\\Tests\\LoadingIcon.cs", "ToolTip": "D:\\Menino Autista\\DKG-RPG_mobile\\Assets\\Resources\\Scripts\\Tests\\LoadingIcon.cs", "RelativeToolTip": "Assets\\Resources\\Scripts\\Tests\\LoadingIcon.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-04-03T10:51:55.916Z"}, {"$type": "Document", "DocumentIndex": 34, "Title": "RandomizeMusic.cs", "DocumentMoniker": "D:\\Menino Autista\\DKG-RPG_mobile\\Assets\\Resources\\Scripts\\SFX\\RandomizeMusic.cs", "RelativeDocumentMoniker": "Assets\\Resources\\Scripts\\SFX\\RandomizeMusic.cs", "ToolTip": "D:\\Menino Autista\\DKG-RPG_mobile\\Assets\\Resources\\Scripts\\SFX\\RandomizeMusic.cs", "RelativeToolTip": "Assets\\Resources\\Scripts\\SFX\\RandomizeMusic.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-04-03T10:51:54.854Z"}, {"$type": "Document", "DocumentIndex": 32, "Title": "TIleDestroy.cs", "DocumentMoniker": "D:\\Menino Autista\\DKG-RPG_mobile\\Assets\\Resources\\Scripts\\MiniGame\\TIleDestroy.cs", "RelativeDocumentMoniker": "Assets\\Resources\\Scripts\\MiniGame\\TIleDestroy.cs", "ToolTip": "D:\\Menino Autista\\DKG-RPG_mobile\\Assets\\Resources\\Scripts\\MiniGame\\TIleDestroy.cs", "RelativeToolTip": "Assets\\Resources\\Scripts\\MiniGame\\TIleDestroy.cs", "ViewState": "AgIAAEEAAAAAAAAAAAAwwFgAAAALAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-04-03T10:51:53.996Z"}, {"$type": "Document", "DocumentIndex": 33, "Title": "TileCollisionHandler.cs", "DocumentMoniker": "D:\\Menino Autista\\DKG-RPG_mobile\\Assets\\Resources\\Scripts\\MiniGame\\TileCollisionHandler.cs", "RelativeDocumentMoniker": "Assets\\Resources\\Scripts\\MiniGame\\TileCollisionHandler.cs", "ToolTip": "D:\\Menino Autista\\DKG-RPG_mobile\\Assets\\Resources\\Scripts\\MiniGame\\TileCollisionHandler.cs", "RelativeToolTip": "Assets\\Resources\\Scripts\\MiniGame\\TileCollisionHandler.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAABsAAAAdAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-04-03T10:51:52.915Z"}, {"$type": "Document", "DocumentIndex": 12, "Title": "Tile.cs", "DocumentMoniker": "D:\\Menino Autista\\DKG-RPG_mobile\\Assets\\Resources\\Scripts\\MiniGame\\Tile.cs", "RelativeDocumentMoniker": "Assets\\Resources\\Scripts\\MiniGame\\Tile.cs", "ToolTip": "D:\\Menino Autista\\DKG-RPG_mobile\\Assets\\Resources\\Scripts\\MiniGame\\Tile.cs", "RelativeToolTip": "Assets\\Resources\\Scripts\\MiniGame\\Tile.cs", "ViewState": "AgIAABUAAAAAAAAAAAAwwCwAAAALAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-04-03T10:51:52.005Z"}, {"$type": "Document", "DocumentIndex": 23, "Title": "RestartGame.cs", "DocumentMoniker": "D:\\Menino Autista\\DKG-RPG_mobile\\Assets\\Resources\\Scripts\\MiniGame\\RestartGame.cs", "RelativeDocumentMoniker": "Assets\\Resources\\Scripts\\MiniGame\\RestartGame.cs", "ToolTip": "D:\\Menino Autista\\DKG-RPG_mobile\\Assets\\Resources\\Scripts\\MiniGame\\RestartGame.cs", "RelativeToolTip": "Assets\\Resources\\Scripts\\MiniGame\\RestartGame.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAkAAAAFAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-04-03T10:51:50.835Z"}, {"$type": "Document", "DocumentIndex": 5, "Title": "Grid.cs", "DocumentMoniker": "D:\\Menino Autista\\DKG-RPG_mobile\\Assets\\Resources\\Scripts\\MiniGame\\Grid.cs", "RelativeDocumentMoniker": "Assets\\Resources\\Scripts\\MiniGame\\Grid.cs", "ToolTip": "D:\\Menino Autista\\DKG-RPG_mobile\\Assets\\Resources\\Scripts\\MiniGame\\Grid.cs", "RelativeToolTip": "Assets\\Resources\\Scripts\\MiniGame\\Grid.cs", "ViewState": "AgIAAH0AAAAAAAAAAAAnwJIAAAAFAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-04-03T10:51:48.705Z"}, {"$type": "Document", "DocumentIndex": 6, "Title": "DamageStar.cs", "DocumentMoniker": "D:\\Menino Autista\\DKG-RPG_mobile\\Assets\\Resources\\Scripts\\MiniGame\\DamageStar.cs", "RelativeDocumentMoniker": "Assets\\Resources\\Scripts\\MiniGame\\DamageStar.cs", "ToolTip": "D:\\Menino Autista\\DKG-RPG_mobile\\Assets\\Resources\\Scripts\\MiniGame\\DamageStar.cs", "RelativeToolTip": "Assets\\Resources\\Scripts\\MiniGame\\DamageStar.cs", "ViewState": "AgIAABEAAAAAAAAAAAAQwA4AAAALAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-04-03T10:51:47.681Z"}, {"$type": "Document", "DocumentIndex": 7, "Title": "AttackEffect.cs", "DocumentMoniker": "D:\\Menino Autista\\DKG-RPG_mobile\\Assets\\Resources\\Scripts\\MiniGame\\AttackEffect.cs", "RelativeDocumentMoniker": "Assets\\Resources\\Scripts\\MiniGame\\AttackEffect.cs", "ToolTip": "D:\\Menino Autista\\DKG-RPG_mobile\\Assets\\Resources\\Scripts\\MiniGame\\AttackEffect.cs", "RelativeToolTip": "Assets\\Resources\\Scripts\\MiniGame\\AttackEffect.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-04-03T10:51:47.079Z"}, {"$type": "Document", "DocumentIndex": 68, "Title": "FPSLocker.cs", "DocumentMoniker": "D:\\Menino Autista\\DKG-RPG_mobile\\Assets\\Resources\\Scripts\\FPSLocker\\FPSLocker.cs", "RelativeDocumentMoniker": "Assets\\Resources\\Scripts\\FPSLocker\\FPSLocker.cs", "ToolTip": "D:\\Menino Autista\\DKG-RPG_mobile\\Assets\\Resources\\Scripts\\FPSLocker\\FPSLocker.cs", "RelativeToolTip": "Assets\\Resources\\Scripts\\FPSLocker\\FPSLocker.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-04-03T10:51:44.893Z"}, {"$type": "Document", "DocumentIndex": 8, "Title": "FPSCounter.cs", "DocumentMoniker": "D:\\Menino Autista\\DKG-RPG_mobile\\Assets\\Resources\\Scripts\\FPSLocker\\FPSCounter.cs", "RelativeDocumentMoniker": "Assets\\Resources\\Scripts\\FPSLocker\\FPSCounter.cs", "ToolTip": "D:\\Menino Autista\\DKG-RPG_mobile\\Assets\\Resources\\Scripts\\FPSLocker\\FPSCounter.cs", "RelativeToolTip": "Assets\\Resources\\Scripts\\FPSLocker\\FPSCounter.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAEAAAASAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-04-03T10:51:44.184Z"}, {"$type": "Document", "DocumentIndex": 9, "Title": "RollsLeft.cs", "DocumentMoniker": "D:\\Menino Autista\\DKG-RPG_mobile\\Assets\\Resources\\Scripts\\DiceSystem\\RollsLeft.cs", "RelativeDocumentMoniker": "Assets\\Resources\\Scripts\\DiceSystem\\RollsLeft.cs", "ToolTip": "D:\\Menino Autista\\DKG-RPG_mobile\\Assets\\Resources\\Scripts\\DiceSystem\\RollsLeft.cs", "RelativeToolTip": "Assets\\Resources\\Scripts\\DiceSystem\\RollsLeft.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-04-03T10:51:43.27Z"}, {"$type": "Document", "DocumentIndex": 10, "Title": "DeathScreen.cs", "DocumentMoniker": "D:\\Menino Autista\\DKG-RPG_mobile\\Assets\\Resources\\Scripts\\DiceSystem\\DeathScreen.cs", "RelativeDocumentMoniker": "Assets\\Resources\\Scripts\\DiceSystem\\DeathScreen.cs", "ToolTip": "D:\\Menino Autista\\DKG-RPG_mobile\\Assets\\Resources\\Scripts\\DiceSystem\\DeathScreen.cs", "RelativeToolTip": "Assets\\Resources\\Scripts\\DiceSystem\\DeathScreen.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-04-03T10:51:42.54Z"}, {"$type": "Document", "DocumentIndex": 11, "Title": "CalculateChances.cs", "DocumentMoniker": "D:\\Menino Autista\\DKG-RPG_mobile\\Assets\\Resources\\Scripts\\DiceSystem\\CalculateChances.cs", "RelativeDocumentMoniker": "Assets\\Resources\\Scripts\\DiceSystem\\CalculateChances.cs", "ToolTip": "D:\\Menino Autista\\DKG-RPG_mobile\\Assets\\Resources\\Scripts\\DiceSystem\\CalculateChances.cs", "RelativeToolTip": "Assets\\Resources\\Scripts\\DiceSystem\\CalculateChances.cs", "ViewState": "AgIAAB8AAAAAAAAAAAAgwAsAAAAaAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-04-03T10:51:41.464Z"}, {"$type": "Document", "DocumentIndex": 69, "Title": "ValuesScroll.cs", "DocumentMoniker": "D:\\Menino Autista\\DKG-RPG_mobile\\Assets\\Resources\\Scripts\\Configs\\ValuesScroll.cs", "RelativeDocumentMoniker": "Assets\\Resources\\Scripts\\Configs\\ValuesScroll.cs", "ToolTip": "D:\\Menino Autista\\DKG-RPG_mobile\\Assets\\Resources\\Scripts\\Configs\\ValuesScroll.cs", "RelativeToolTip": "Assets\\Resources\\Scripts\\Configs\\ValuesScroll.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-04-03T10:51:40.306Z"}, {"$type": "Document", "DocumentIndex": 29, "Title": "SwitchTurnsAnimation.cs", "DocumentMoniker": "D:\\Menino Autista\\DKG-RPG_mobile\\Assets\\Resources\\Scripts\\Configs\\SwitchTurnsAnimation.cs", "RelativeDocumentMoniker": "Assets\\Resources\\Scripts\\Configs\\SwitchTurnsAnimation.cs", "ToolTip": "D:\\Menino Autista\\DKG-RPG_mobile\\Assets\\Resources\\Scripts\\Configs\\SwitchTurnsAnimation.cs", "RelativeToolTip": "Assets\\Resources\\Scripts\\Configs\\SwitchTurnsAnimation.cs", "ViewState": "AgIAAAIAAAAAAAAAAAAgwBcAAAAIAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-04-03T10:51:39.548Z"}, {"$type": "Document", "DocumentIndex": 70, "Title": "SwitchConfigs.cs", "DocumentMoniker": "D:\\Menino Autista\\DKG-RPG_mobile\\Assets\\Resources\\Scripts\\Configs\\SwitchConfigs.cs", "RelativeDocumentMoniker": "Assets\\Resources\\Scripts\\Configs\\SwitchConfigs.cs", "ToolTip": "D:\\Menino Autista\\DKG-RPG_mobile\\Assets\\Resources\\Scripts\\Configs\\SwitchConfigs.cs", "RelativeToolTip": "Assets\\Resources\\Scripts\\Configs\\SwitchConfigs.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-04-03T10:51:38.286Z"}, {"$type": "Document", "DocumentIndex": 20, "Title": "SwitchCameras.cs", "DocumentMoniker": "D:\\Menino Autista\\DKG-RPG_mobile\\Assets\\Resources\\Scripts\\Configs\\SwitchCameras.cs", "RelativeDocumentMoniker": "Assets\\Resources\\Scripts\\Configs\\SwitchCameras.cs", "ToolTip": "D:\\Menino Autista\\DKG-RPG_mobile\\Assets\\Resources\\Scripts\\Configs\\SwitchCameras.cs", "RelativeToolTip": "Assets\\Resources\\Scripts\\Configs\\SwitchCameras.cs", "ViewState": "AgIAAAgAAAAAAAAAAAAgwBYAAABTAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-04-03T10:51:37.579Z"}, {"$type": "Document", "DocumentIndex": 71, "Title": "StatsValueDisplay.cs", "DocumentMoniker": "D:\\Menino Autista\\DKG-RPG_mobile\\Assets\\Resources\\Scripts\\Configs\\StatsValueDisplay.cs", "RelativeDocumentMoniker": "Assets\\Resources\\Scripts\\Configs\\StatsValueDisplay.cs", "ToolTip": "D:\\Menino Autista\\DKG-RPG_mobile\\Assets\\Resources\\Scripts\\Configs\\StatsValueDisplay.cs", "RelativeToolTip": "Assets\\Resources\\Scripts\\Configs\\StatsValueDisplay.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-04-03T10:51:36.744Z"}, {"$type": "Document", "DocumentIndex": 15, "Title": "SkillsValueDisplay.cs", "DocumentMoniker": "D:\\Menino Autista\\DKG-RPG_mobile\\Assets\\Resources\\Scripts\\Configs\\SkillsValueDisplay.cs", "RelativeDocumentMoniker": "Assets\\Resources\\Scripts\\Configs\\SkillsValueDisplay.cs", "ToolTip": "D:\\Menino Autista\\DKG-RPG_mobile\\Assets\\Resources\\Scripts\\Configs\\SkillsValueDisplay.cs", "RelativeToolTip": "Assets\\Resources\\Scripts\\Configs\\SkillsValueDisplay.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-04-03T10:51:35.99Z"}, {"$type": "Document", "DocumentIndex": 72, "Title": "PasteButton.cs", "DocumentMoniker": "D:\\Menino Autista\\DKG-RPG_mobile\\Assets\\Resources\\Scripts\\Configs\\PasteButton.cs", "RelativeDocumentMoniker": "Assets\\Resources\\Scripts\\Configs\\PasteButton.cs", "ToolTip": "D:\\Menino Autista\\DKG-RPG_mobile\\Assets\\Resources\\Scripts\\Configs\\PasteButton.cs", "RelativeToolTip": "Assets\\Resources\\Scripts\\Configs\\PasteButton.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-04-03T10:51:34.92Z"}, {"$type": "Document", "DocumentIndex": 30, "Title": "PartyConfigs.cs", "DocumentMoniker": "D:\\Menino Autista\\DKG-RPG_mobile\\Assets\\Resources\\Scripts\\Configs\\PartyConfigs.cs", "RelativeDocumentMoniker": "Assets\\Resources\\Scripts\\Configs\\PartyConfigs.cs", "ToolTip": "D:\\Menino Autista\\DKG-RPG_mobile\\Assets\\Resources\\Scripts\\Configs\\PartyConfigs.cs", "RelativeToolTip": "Assets\\Resources\\Scripts\\Configs\\PartyConfigs.cs", "ViewState": "AgIAACEAAAAAAAAAAAAIwDgAAAA4AAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-04-03T10:51:34.044Z"}, {"$type": "Document", "DocumentIndex": 73, "Title": "PartyCharacters.cs", "DocumentMoniker": "D:\\Menino Autista\\DKG-RPG_mobile\\Assets\\Resources\\Scripts\\Configs\\PartyCharacters.cs", "RelativeDocumentMoniker": "Assets\\Resources\\Scripts\\Configs\\PartyCharacters.cs", "ToolTip": "D:\\Menino Autista\\DKG-RPG_mobile\\Assets\\Resources\\Scripts\\Configs\\PartyCharacters.cs", "RelativeToolTip": "Assets\\Resources\\Scripts\\Configs\\PartyCharacters.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-04-03T10:51:28.025Z"}, {"$type": "Document", "DocumentIndex": 31, "Title": "ModsValueDisplay.cs", "DocumentMoniker": "D:\\Menino Autista\\DKG-RPG_mobile\\Assets\\Resources\\Scripts\\Configs\\ModsValueDisplay.cs", "RelativeDocumentMoniker": "Assets\\Resources\\Scripts\\Configs\\ModsValueDisplay.cs", "ToolTip": "D:\\Menino Autista\\DKG-RPG_mobile\\Assets\\Resources\\Scripts\\Configs\\ModsValueDisplay.cs", "RelativeToolTip": "Assets\\Resources\\Scripts\\Configs\\ModsValueDisplay.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-04-03T10:51:27.36Z"}, {"$type": "Document", "DocumentIndex": 24, "Title": "LoadValues.cs", "DocumentMoniker": "D:\\Menino Autista\\DKG-RPG_mobile\\Assets\\Resources\\Scripts\\Configs\\LoadValues.cs", "RelativeDocumentMoniker": "Assets\\Resources\\Scripts\\Configs\\LoadValues.cs", "ToolTip": "D:\\Menino Autista\\DKG-RPG_mobile\\Assets\\Resources\\Scripts\\Configs\\LoadValues.cs", "RelativeToolTip": "Assets\\Resources\\Scripts\\Configs\\LoadValues.cs", "ViewState": "AgIAAN0BAAAAAAAAAAD4v/UBAAAXAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-04-03T10:51:26.71Z"}, {"$type": "Document", "DocumentIndex": 18, "Title": "GoldenStrike.cs", "DocumentMoniker": "D:\\Menino Autista\\DKG-RPG_mobile\\Assets\\Resources\\Scripts\\Configs\\GoldenStrike.cs", "RelativeDocumentMoniker": "Assets\\Resources\\Scripts\\Configs\\GoldenStrike.cs", "ToolTip": "D:\\Menino Autista\\DKG-RPG_mobile\\Assets\\Resources\\Scripts\\Configs\\GoldenStrike.cs", "RelativeToolTip": "Assets\\Resources\\Scripts\\Configs\\GoldenStrike.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-04-03T10:51:25.886Z"}, {"$type": "Document", "DocumentIndex": 3, "Title": "EditStockWeight.cs", "DocumentMoniker": "D:\\Menino Autista\\DKG-RPG_mobile\\Assets\\Resources\\Scripts\\Configs\\EditStockWeight.cs", "RelativeDocumentMoniker": "Assets\\Resources\\Scripts\\Configs\\EditStockWeight.cs", "ToolTip": "D:\\Menino Autista\\DKG-RPG_mobile\\Assets\\Resources\\Scripts\\Configs\\EditStockWeight.cs", "RelativeToolTip": "Assets\\Resources\\Scripts\\Configs\\EditStockWeight.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-04-03T10:51:25.189Z"}, {"$type": "Document", "DocumentIndex": 74, "Title": "EditReductPerPiece.cs", "DocumentMoniker": "D:\\Menino Autista\\DKG-RPG_mobile\\Assets\\Resources\\Scripts\\Configs\\EditReductPerPiece.cs", "RelativeDocumentMoniker": "Assets\\Resources\\Scripts\\Configs\\EditReductPerPiece.cs", "ToolTip": "D:\\Menino Autista\\DKG-RPG_mobile\\Assets\\Resources\\Scripts\\Configs\\EditReductPerPiece.cs", "RelativeToolTip": "Assets\\Resources\\Scripts\\Configs\\EditReductPerPiece.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-04-03T10:51:24.336Z"}, {"$type": "Document", "DocumentIndex": 14, "Title": "EditMaxPP.cs", "DocumentMoniker": "D:\\Menino Autista\\DKG-RPG_mobile\\Assets\\Resources\\Scripts\\Configs\\EditMaxPP.cs", "RelativeDocumentMoniker": "Assets\\Resources\\Scripts\\Configs\\EditMaxPP.cs", "ToolTip": "D:\\Menino Autista\\DKG-RPG_mobile\\Assets\\Resources\\Scripts\\Configs\\EditMaxPP.cs", "RelativeToolTip": "Assets\\Resources\\Scripts\\Configs\\EditMaxPP.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-04-03T10:51:23.667Z"}, {"$type": "Document", "DocumentIndex": 13, "Title": "EditLetterValues.cs", "DocumentMoniker": "D:\\Menino Autista\\DKG-RPG_mobile\\Assets\\Resources\\Scripts\\Configs\\EditLetterValues.cs", "RelativeDocumentMoniker": "Assets\\Resources\\Scripts\\Configs\\EditLetterValues.cs", "ToolTip": "D:\\Menino Autista\\DKG-RPG_mobile\\Assets\\Resources\\Scripts\\Configs\\EditLetterValues.cs", "RelativeToolTip": "Assets\\Resources\\Scripts\\Configs\\EditLetterValues.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-04-03T10:51:21.608Z"}, {"$type": "Document", "DocumentIndex": 17, "Title": "EditIDBOffset.cs", "DocumentMoniker": "D:\\Menino Autista\\DKG-RPG_mobile\\Assets\\Resources\\Scripts\\Configs\\EditIDBOffset.cs", "RelativeDocumentMoniker": "Assets\\Resources\\Scripts\\Configs\\EditIDBOffset.cs", "ToolTip": "D:\\Menino Autista\\DKG-RPG_mobile\\Assets\\Resources\\Scripts\\Configs\\EditIDBOffset.cs", "RelativeToolTip": "Assets\\Resources\\Scripts\\Configs\\EditIDBOffset.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-04-03T10:51:15.01Z"}, {"$type": "Document", "DocumentIndex": 75, "Title": "EditFractionPerAction.cs", "DocumentMoniker": "D:\\Menino Autista\\DKG-RPG_mobile\\Assets\\Resources\\Scripts\\Configs\\EditFractionPerAction.cs", "RelativeDocumentMoniker": "Assets\\Resources\\Scripts\\Configs\\EditFractionPerAction.cs", "ToolTip": "D:\\Menino Autista\\DKG-RPG_mobile\\Assets\\Resources\\Scripts\\Configs\\EditFractionPerAction.cs", "RelativeToolTip": "Assets\\Resources\\Scripts\\Configs\\EditFractionPerAction.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-04-03T10:51:05.849Z"}, {"$type": "Document", "DocumentIndex": 76, "Title": "EditFractionMod.cs", "DocumentMoniker": "D:\\Menino Autista\\DKG-RPG_mobile\\Assets\\Resources\\Scripts\\Configs\\EditFractionMod.cs", "RelativeDocumentMoniker": "Assets\\Resources\\Scripts\\Configs\\EditFractionMod.cs", "ToolTip": "D:\\Menino Autista\\DKG-RPG_mobile\\Assets\\Resources\\Scripts\\Configs\\EditFractionMod.cs", "RelativeToolTip": "Assets\\Resources\\Scripts\\Configs\\EditFractionMod.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-04-03T10:51:05.16Z"}, {"$type": "Document", "DocumentIndex": 77, "Title": "EditEnergyTime.cs", "DocumentMoniker": "D:\\Menino Autista\\DKG-RPG_mobile\\Assets\\Resources\\Scripts\\Configs\\EditEnergyTime.cs", "RelativeDocumentMoniker": "Assets\\Resources\\Scripts\\Configs\\EditEnergyTime.cs", "ToolTip": "D:\\Menino Autista\\DKG-RPG_mobile\\Assets\\Resources\\Scripts\\Configs\\EditEnergyTime.cs", "RelativeToolTip": "Assets\\Resources\\Scripts\\Configs\\EditEnergyTime.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-04-03T10:51:04.501Z"}, {"$type": "Document", "DocumentIndex": 78, "Title": "EditDifficulty.cs", "DocumentMoniker": "D:\\Menino Autista\\DKG-RPG_mobile\\Assets\\Resources\\Scripts\\Configs\\EditDifficulty.cs", "RelativeDocumentMoniker": "Assets\\Resources\\Scripts\\Configs\\EditDifficulty.cs", "ToolTip": "D:\\Menino Autista\\DKG-RPG_mobile\\Assets\\Resources\\Scripts\\Configs\\EditDifficulty.cs", "RelativeToolTip": "Assets\\Resources\\Scripts\\Configs\\EditDifficulty.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-04-03T10:51:03.199Z"}, {"$type": "Document", "DocumentIndex": 35, "Title": "ComboChance.cs", "DocumentMoniker": "D:\\Menino Autista\\DKG-RPG_mobile\\Assets\\Resources\\Scripts\\Configs\\ComboChance.cs", "RelativeDocumentMoniker": "Assets\\Resources\\Scripts\\Configs\\ComboChance.cs", "ToolTip": "D:\\Menino Autista\\DKG-RPG_mobile\\Assets\\Resources\\Scripts\\Configs\\ComboChance.cs", "RelativeToolTip": "Assets\\Resources\\Scripts\\Configs\\ComboChance.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-04-03T10:51:01.693Z"}, {"$type": "Document", "DocumentIndex": 19, "Title": "ChangeBetweenConfigs.cs", "DocumentMoniker": "D:\\Menino Autista\\DKG-RPG_mobile\\Assets\\Resources\\Scripts\\Configs\\ChangeBetweenConfigs.cs", "RelativeDocumentMoniker": "Assets\\Resources\\Scripts\\Configs\\ChangeBetweenConfigs.cs", "ToolTip": "D:\\Menino Autista\\DKG-RPG_mobile\\Assets\\Resources\\Scripts\\Configs\\ChangeBetweenConfigs.cs", "RelativeToolTip": "Assets\\Resources\\Scripts\\Configs\\ChangeBetweenConfigs.cs", "ViewState": "AgIAAAYAAAAAAAAAAAAhwBsAAABpAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-04-03T10:51:00.949Z"}, {"$type": "Document", "DocumentIndex": 16, "Title": "BuffNDeBuffs.cs", "DocumentMoniker": "D:\\Menino Autista\\DKG-RPG_mobile\\Assets\\Resources\\Scripts\\Configs\\BuffNDeBuffs.cs", "RelativeDocumentMoniker": "Assets\\Resources\\Scripts\\Configs\\BuffNDeBuffs.cs", "ToolTip": "D:\\Menino Autista\\DKG-RPG_mobile\\Assets\\Resources\\Scripts\\Configs\\BuffNDeBuffs.cs", "RelativeToolTip": "Assets\\Resources\\Scripts\\Configs\\BuffNDeBuffs.cs", "ViewState": "AgIAAAEAAAAAAAAAAAAiwAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-04-03T10:50:59.973Z"}, {"$type": "Document", "DocumentIndex": 36, "Title": "AddCharButton.cs", "DocumentMoniker": "D:\\Menino Autista\\DKG-RPG_mobile\\Assets\\Resources\\Scripts\\Configs\\AddCharButton.cs", "RelativeDocumentMoniker": "Assets\\Resources\\Scripts\\Configs\\AddCharButton.cs", "ToolTip": "D:\\Menino Autista\\DKG-RPG_mobile\\Assets\\Resources\\Scripts\\Configs\\AddCharButton.cs", "RelativeToolTip": "Assets\\Resources\\Scripts\\Configs\\AddCharButton.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-04-03T10:50:59.071Z"}, {"$type": "Document", "DocumentIndex": 37, "Title": "AddBuffButton.cs", "DocumentMoniker": "D:\\Menino Autista\\DKG-RPG_mobile\\Assets\\Resources\\Scripts\\Configs\\AddBuffButton.cs", "RelativeDocumentMoniker": "Assets\\Resources\\Scripts\\Configs\\AddBuffButton.cs", "ToolTip": "D:\\Menino Autista\\DKG-RPG_mobile\\Assets\\Resources\\Scripts\\Configs\\AddBuffButton.cs", "RelativeToolTip": "Assets\\Resources\\Scripts\\Configs\\AddBuffButton.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-04-03T10:50:58.414Z"}, {"$type": "Document", "DocumentIndex": 22, "Title": "EnemyAnimation.cs", "DocumentMoniker": "D:\\Menino Autista\\DKG-RPG_mobile\\Assets\\Resources\\Scripts\\Characters\\EnemyAnimation.cs", "RelativeDocumentMoniker": "Assets\\Resources\\Scripts\\Characters\\EnemyAnimation.cs", "ToolTip": "D:\\Menino Autista\\DKG-RPG_mobile\\Assets\\Resources\\Scripts\\Characters\\EnemyAnimation.cs", "RelativeToolTip": "Assets\\Resources\\Scripts\\Characters\\EnemyAnimation.cs", "ViewState": "AgIAAAcAAAAAAAAAAAAgwI4AAAALAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-04-03T10:50:51.081Z"}, {"$type": "Document", "DocumentIndex": 38, "Title": "CharacterStatus.cs", "DocumentMoniker": "D:\\Menino Autista\\DKG-RPG_mobile\\Assets\\Resources\\Scripts\\Characters\\CharacterStatus.cs", "RelativeDocumentMoniker": "Assets\\Resources\\Scripts\\Characters\\CharacterStatus.cs", "ToolTip": "D:\\Menino Autista\\DKG-RPG_mobile\\Assets\\Resources\\Scripts\\Characters\\CharacterStatus.cs", "RelativeToolTip": "Assets\\Resources\\Scripts\\Characters\\CharacterStatus.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-04-03T10:50:48.503Z"}, {"$type": "Document", "DocumentIndex": 39, "Title": "CharacterSkills.cs", "DocumentMoniker": "D:\\Menino Autista\\DKG-RPG_mobile\\Assets\\Resources\\Scripts\\Characters\\CharacterSkills.cs", "RelativeDocumentMoniker": "Assets\\Resources\\Scripts\\Characters\\CharacterSkills.cs", "ToolTip": "D:\\Menino Autista\\DKG-RPG_mobile\\Assets\\Resources\\Scripts\\Characters\\CharacterSkills.cs", "RelativeToolTip": "Assets\\Resources\\Scripts\\Characters\\CharacterSkills.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-04-03T10:50:46.201Z"}, {"$type": "Document", "DocumentIndex": 40, "Title": "CharacterMods.cs", "DocumentMoniker": "D:\\Menino Autista\\DKG-RPG_mobile\\Assets\\Resources\\Scripts\\Characters\\CharacterMods.cs", "RelativeDocumentMoniker": "Assets\\Resources\\Scripts\\Characters\\CharacterMods.cs", "ToolTip": "D:\\Menino Autista\\DKG-RPG_mobile\\Assets\\Resources\\Scripts\\Characters\\CharacterMods.cs", "RelativeToolTip": "Assets\\Resources\\Scripts\\Characters\\CharacterMods.cs", "ViewState": "AgIAAAAAAAAAAAAAAAA6wAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-04-03T10:50:33.206Z"}, {"$type": "Document", "DocumentIndex": 1, "Title": "BattleCharacter.cs", "DocumentMoniker": "D:\\Menino Autista\\DKG-RPG_mobile\\Assets\\Resources\\Scripts\\Characters\\BattleCharacter.cs", "RelativeDocumentMoniker": "Assets\\Resources\\Scripts\\Characters\\BattleCharacter.cs", "ToolTip": "D:\\Menino Autista\\DKG-RPG_mobile\\Assets\\Resources\\Scripts\\Characters\\BattleCharacter.cs", "RelativeToolTip": "Assets\\Resources\\Scripts\\Characters\\BattleCharacter.cs", "ViewState": "AgIAAJgAAAAAAAAAAAAwwLIAAAAiAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-04-03T10:50:12.178Z"}]}]}]}