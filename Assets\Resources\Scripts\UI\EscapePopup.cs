using UnityEngine;
using UnityEngine.UI;

public class EscapePopup : MonoBehaviour
{
    Button escapeB, cancelB;

    ConfigsHandler configsHandler; // Reference to the ConfigsHandler

    void Start()
    {
        configsHandler = GameObject.Find("GameConfigsHandler").GetComponent<ConfigsHandler>(); // Get the ConfigsHandler script

        // Get the buttons
        escapeB = transform.GetChild(2).GetComponent<Button>();
        cancelB = transform.GetChild(3).GetComponent<Button>();

        // Add the listeners to the buttons
        escapeB.onClick.AddListener(OnEscapeClick);
        cancelB.onClick.AddListener(OnCancelClick);
    }

    public void OnEscapeClick() // Tries to escape from the battle
    {
        configsHandler.TryToEscape();
        configsHandler.EscapePopup.SetActive(false);
    }

    public void OnCancelClick() // Cancels the escape
    {
        configsHandler.EscapePopup.SetActive(false);
    }
}
