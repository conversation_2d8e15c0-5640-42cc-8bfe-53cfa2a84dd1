using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;
using TMPro;
using System.Collections;
using System.Linq;
using DG.Tweening;

/// <summary>
/// Class that handles the party configs
/// </summary>
public class PartyConfigs : MonoBehaviour
{
    // ConfigsHandler reference
    ConfigsHandler configsHandler;

    // ToggleGroups of the party being used and the party that is being eddited
    ToggleGroup useParty, editParty;
    TMP_InputField partyName;

    // Single party GameObject that will be dynamically updated
    public GameObject singlePartyOBJ;
    public GameObject activePlayers;
    public GameObject stockPlayers;

    GridLayoutGroup activeLayout, stockLayout;

    public GameObject cardPrefab;

    // Bool that checks if the game has started
    bool didGameStart = false;

    // Int that controls the index of the party
    public int partyIndex = 0;

    // Dictionary that controls the colors of the rarities
    private readonly Dictionary<string, Color> RarityColors = new();

    void Start()
    {
        // Get the ConfigsHandler script
        configsHandler = GameObject.Find("GameConfigsHandler").GetComponent<ConfigsHandler>();

        // Get the ToggleGroup of the party being used
        useParty = GetComponent<ToggleGroup>();

        // Get the ToggleGroup of the party being eddited
        editParty = transform.parent.parent.GetChild(1).GetComponent<ToggleGroup>();

        // Add the onEndEdit listener to the TMP_InputField
        partyName = singlePartyOBJ.transform.GetChild(0).GetComponent<TMP_InputField>();
        partyName.onEndEdit.AddListener(text => PartyNameEditted(text, partyName));

        activePlayers = singlePartyOBJ.transform.GetChild(3).GetChild(1).gameObject;
        stockPlayers = singlePartyOBJ.transform.GetChild(4).GetChild(0).gameObject;
        activeLayout = activePlayers.GetComponent<GridLayoutGroup>();
        stockLayout = stockPlayers.GetComponent<GridLayoutGroup>();

        // Initialize RarityColors dictionary
        RarityColors.Add("Comum", GeneralInfo.GetTierColor("TR2"));
        RarityColors.Add("Inferior", GeneralInfo.GetTierColor("TR5"));
        RarityColors.Add("Raro", GeneralInfo.GetTierColor("TR3"));
        RarityColors.Add("Épico", GeneralInfo.GetTierColor("TR1"));
        RarityColors.Add("Lendário", GeneralInfo.GetTierColor("TR6"));
        RarityColors.Add("Elementar", GeneralInfo.GetTierColor("TR4"));

        LoadPrefabs();

        // Setup "select party" toggle listeners
        SetupSelectPartyToggleListeners();

        // Update the UI to each party
        Toggle[] editPartyToggles = editParty.GetComponentsInChildren<Toggle>();
        foreach (var toggle in editPartyToggles)
        {
            toggle.onValueChanged.AddListener((isOn) =>
            {
                // play the select sound effect
                ConfMenu.PlaySoundWithVolume(GameObject.Find("GameConfigsHandler").GetComponent<AudioSource>(), Resources.Load<AudioClip>("SFX/MenuSound"));

                if (isOn)
                {
                    // Delay slightly to ensure the ToggleGroup has updated
                    StartCoroutine(RefreshNextFrame());
                }
            });
        }
    }

    IEnumerator RefreshNextFrame() // Without this you would have to click the toggle twice
    {
        yield return null; // wait one frame
        UpdateAllCardsUI(); // This will now update the single GameObject with data from the selected party

        PartyCharacters partyCharacters = configsHandler.GetPartyCharacters(partyIndex);
        partyName.text = partyCharacters.name;

        // Reset the "select party" toggle group when switching between different parties
        ResetSelectPartyToggles();
    }

    void SetupSelectPartyToggleListeners()
    {
        if (singlePartyOBJ == null) return;

        // Get the single "select party" toggle at child index 3
        Transform selectPartyTransform = singlePartyOBJ.transform.GetChild(5);
        Toggle selectPartyToggle = selectPartyTransform.GetComponent<Toggle>();

        if (selectPartyToggle == null) return;

        // Clear existing listeners to avoid duplicates
        selectPartyToggle.onValueChanged.RemoveAllListeners();

        selectPartyToggle.onValueChanged.AddListener((isOn) =>
        {
            // play the select sound effect
            ConfMenu.PlaySoundWithVolume(GameObject.Find("GameConfigsHandler").GetComponent<AudioSource>(), Resources.Load<AudioClip>("SFX/MenuSound"));

            if (isOn)
            {
                // Set the selected party to the currently displayed party (partyIndex)
                configsHandler.SetSelectedParty(partyIndex);
            }
            else
            {
                // If toggle is turned off, set selected party to -1 (no party selected)
                configsHandler.SetSelectedParty(-1);
            }
        });
    }

    void ResetSelectPartyToggles()
    {
        if (singlePartyOBJ == null) return;

        // Get the single "select party" toggle at child index 3
        Transform selectPartyTransform = singlePartyOBJ.transform.GetChild(3);
        Toggle selectPartyToggle = selectPartyTransform.GetComponent<Toggle>();

        if (selectPartyToggle == null) return;

        // Turn off the select party toggle
        selectPartyToggle.isOn = false;

        // Set selected party to -1 (no party selected)
        configsHandler.SetSelectedParty(-1);
    }

    void LoadPrefabs()
    {
        // Instantiates the 4 prefabs
        for (int i = 0; i < 4; i++)
        {
            Instantiate(cardPrefab, activePlayers.transform);
            activePlayers.transform.GetChild(i).GetChild(1).GetComponent<PlayerCard>().Init(true, i, "AP" + i);


        }

        for (int i = 0; i < 12; i++)
        {
            Instantiate(cardPrefab, stockPlayers.transform);
            stockPlayers.transform.GetChild(i).GetChild(1).GetComponent<PlayerCard>().Init(false, i, "SP" + i);

        }

        StartCoroutine(FinalizeGridLayout(activePlayers, activeLayout));
        StartCoroutine(FinalizeGridLayout(stockPlayers, stockLayout));
    }

    IEnumerator FinalizeGridLayout(GameObject parent, GridLayoutGroup gridLayoutGroup)
    {
        // Wait for layout to complete
        yield return new WaitForEndOfFrame();

        List<Vector3> cachedLocalPositions = new List<Vector3>();

        foreach (Transform child in parent.transform)
        {
            cachedLocalPositions.Add(child.localPosition);
        }

        // Disable GridLayoutGroup so we can animate freely
        gridLayoutGroup.enabled = false;

        // Reapply positions (to "bake in" the layout)
        int i = 0;
        foreach (Transform child in parent.transform)
        {
            child.localPosition = cachedLocalPositions[i];
            i++;
        }

        // Reset grid bounds calculation flag so it gets recalculated with new positions
        //gridBoundsCalculated = false;
    }

    void Update()
    {
        if (configsHandler.turns != 1) didGameStart = true; // checks if the game has started

        // if the game has started, disables the toggles to select the party
        if (didGameStart) foreach (var useToggle in useParty.gameObject.GetComponentsInChildren<Toggle>()) useToggle.interactable = false;

        // Update speed display for the single party GameObject
        if (singlePartyOBJ != null)
        {
            singlePartyOBJ.transform.GetChild(6).GetChild(1).GetComponent<TextMeshProUGUI>().text = configsHandler.CalculateAverageActivePartySpeed().ToString() == "NaN" ? "Select party" : configsHandler.CalculateAverageActivePartySpeed().ToString();
        }

        // gets the index of the party
        partyIndex = editParty.ActiveToggles().FirstOrDefault().transform.GetSiblingIndex();


    }

    public void SwapCharacters(PlayerCard draggedCard, PlayerCard targetCard)
    {

        if (draggedCard == null || targetCard == null) return;

        if (draggedCard.isInActive && targetCard.isInActive) // if both cards are in the same section (active)
        {
            // Store indices before swap
            int draggedIndex = draggedCard.partyCharacterIndex;
            int targetIndex = targetCard.partyCharacterIndex;

            // Swap characters in the party data
            BattleCharacter temp = draggedCard.character;
            configsHandler.SetCharacterToParty(partyIndex, draggedIndex, draggedCard.isInActive, targetCard.character);
            configsHandler.SetCharacterToParty(partyIndex, targetIndex, targetCard.isInActive, temp);

            // Update indices on the card objects
            draggedCard.partyCharacterIndex = targetIndex;
            targetCard.partyCharacterIndex = draggedIndex;

            // Store positions for animation
            Vector3 draggedLocalPos = draggedCard.initialPosition;
            Vector3 targetLocalPos = targetCard.gameObject.transform.parent.localPosition;

            // Update initial positions
            draggedCard.initialPosition = targetCard.initialPosition;
            targetCard.initialPosition = draggedLocalPos;

            //Set Parent and Sibling Index to maintain proper order
            draggedCard.gameObject.transform.parent.SetParent(activePlayers.transform);
            draggedCard.gameObject.transform.parent.SetSiblingIndex(draggedCard.partyCharacterIndex);
            targetCard.gameObject.transform.parent.SetSiblingIndex(targetCard.partyCharacterIndex);

            // Animate moves
            draggedCard.gameObject.transform.parent.DOLocalMove(targetLocalPos, 0.3f).SetEase(Ease.OutCubic);
            targetCard.gameObject.transform.parent.DOLocalMove(draggedLocalPos, 0.3f).SetEase(Ease.OutCubic);

            // Update UI after swapping indices
            UpdateAllCardsUI();
        }
        else if (!draggedCard.isInActive && !targetCard.isInActive) // if both cards are in the stock section
        {
            // Store indices before swap
            int draggedIndex = draggedCard.partyCharacterIndex;
            int targetIndex = targetCard.partyCharacterIndex;

            // Swap characters in the party data
            BattleCharacter temp = draggedCard.character;
            configsHandler.SetCharacterToParty(partyIndex, draggedIndex, draggedCard.isInActive, targetCard.character);
            configsHandler.SetCharacterToParty(partyIndex, targetIndex, targetCard.isInActive, temp);

            // Update indices on the card objects
            draggedCard.partyCharacterIndex = targetIndex;
            targetCard.partyCharacterIndex = draggedIndex;

            // Store positions for animation
            Vector3 draggedLocalPos = draggedCard.initialPosition;
            Vector3 targetLocalPos = targetCard.gameObject.transform.parent.localPosition;

            // Update initial positions
            draggedCard.initialPosition = targetCard.initialPosition;
            targetCard.initialPosition = draggedLocalPos;

            //Set Parent and Sibling Index to maintain proper order
            draggedCard.gameObject.transform.parent.SetParent(stockPlayers.transform);
            draggedCard.gameObject.transform.parent.SetSiblingIndex(draggedCard.partyCharacterIndex);
            targetCard.gameObject.transform.parent.SetSiblingIndex(targetCard.partyCharacterIndex);

            // Animate moves
            draggedCard.gameObject.transform.parent.DOLocalMove(targetLocalPos, 0.3f).SetEase(Ease.OutCubic);
            targetCard.gameObject.transform.parent.DOLocalMove(draggedLocalPos, 0.3f).SetEase(Ease.OutCubic);

            // Update UI after swapping indices
            UpdateAllCardsUI();
        }
        else // Cross-section swap (active <-> stock)
        {
            if (draggedCard.isInActive && targetCard.charInStockButAlsoInActive) // if the dragged card is in the active and the target card is in the stock but also in the active, do nothing
            {
                draggedCard.gameObject.transform.parent.SetParent(activePlayers.transform);
                draggedCard.gameObject.transform.parent.SetSiblingIndex(draggedCard.partyCharacterIndex);
                draggedCard.gameObject.transform.parent.DOLocalMove(draggedCard.initialPosition, 0.3f).SetEase(Ease.OutCubic);
                return;
            }

            if (draggedCard.charInStockButAlsoInActive && targetCard.isInActive) // if the dragged card is in the stock but also in the active and the target card is in the active, do nothing
            {
                draggedCard.gameObject.transform.parent.SetParent(stockPlayers.transform);
                draggedCard.gameObject.transform.parent.SetSiblingIndex(draggedCard.partyCharacterIndex);
                draggedCard.gameObject.transform.parent.DOLocalMove(draggedCard.initialPosition, 0.3f).SetEase(Ease.OutCubic);
                return;
            }

            // Store original values before swapping
                bool draggedOriginalIsActive = draggedCard.isInActive;
            bool targetOriginalIsActive = targetCard.isInActive;
            int draggedOriginalIndex = draggedCard.partyCharacterIndex;
            int targetOriginalIndex = targetCard.partyCharacterIndex;

            // Swap characters in party data
            configsHandler.SetCharacterToParty(partyIndex, draggedOriginalIndex, draggedOriginalIsActive, targetCard.character);
            configsHandler.SetCharacterToParty(partyIndex, targetOriginalIndex, targetOriginalIsActive, draggedCard.character);

            // Update card properties to their new positions
            draggedCard.isInActive = targetOriginalIsActive;
            draggedCard.partyCharacterIndex = targetOriginalIndex;
            targetCard.isInActive = draggedOriginalIsActive;
            targetCard.partyCharacterIndex = draggedOriginalIndex;

            // Move GameObjects to their new parents
            Transform draggedNewParent = draggedCard.isInActive ? activePlayers.transform : stockPlayers.transform;
            Transform targetNewParent = targetCard.isInActive ? activePlayers.transform : stockPlayers.transform;

            // Store target positions before changing parents
            Vector3 draggedTargetPos = targetCard.gameObject.transform.parent.localPosition;
            Vector3 targetTargetPos = draggedCard.initialPosition;

            // Change parents
            draggedCard.gameObject.transform.parent.SetParent(draggedNewParent);
            targetCard.gameObject.transform.parent.SetParent(targetNewParent);

            // Set sibling indices to maintain proper order
            draggedCard.gameObject.transform.parent.SetSiblingIndex(draggedCard.partyCharacterIndex);
            targetCard.gameObject.transform.parent.SetSiblingIndex(targetCard.partyCharacterIndex);

            // Animate to new positions

            draggedCard.gameObject.transform.parent.DOLocalMove(draggedTargetPos, 0.3f).SetEase(Ease.OutCubic);
            targetCard.gameObject.transform.parent.DOLocalMove(targetTargetPos, 0.3f).SetEase(Ease.OutCubic);

            //Also change stock equivalent
            if (draggedCard.character != null && targetCard.character != null)
            {
                if (draggedCard.isInActive)
                {
                    PlayerCard stockEquivalent = null;
                    foreach (var card in stockPlayers.GetComponentsInChildren<PlayerCard>())
                    {
                        if (card.character == null) continue;

                        if (card.character.id == targetCard.character.id)
                        {
                            stockEquivalent = card;
                            stockEquivalent.charInStockButAlsoInActive = true;
                            break;
                        }
                    }

                    //Set stock equivalent
                    configsHandler.SetCharacterToParty(partyIndex, stockEquivalent.partyCharacterIndex, stockEquivalent.isInActive, draggedCard.character);
                }
                else
                {
                    PlayerCard stockEquivalent = null;
                    foreach (var card in stockPlayers.GetComponentsInChildren<PlayerCard>())
                    {
                        if (card.character == null) continue;

                        if (card.character.id == draggedCard.character.id)
                        {
                            stockEquivalent = card;
                            stockEquivalent.charInStockButAlsoInActive = true;
                            break;
                        }
                    }

                    //Set stock equivalent
                    configsHandler.SetCharacterToParty(partyIndex, stockEquivalent.partyCharacterIndex, stockEquivalent.isInActive, targetCard.character);
                }
            }
            else if (draggedCard.character != null && targetCard.character == null)
            {
                if (draggedCard.isInActive)
                {
                    configsHandler.SetCharacterToParty(partyIndex, targetCard.partyCharacterIndex, targetCard.isInActive, draggedCard.character);
                    targetCard.charInStockButAlsoInActive = true;
                }
                else
                {
                    PlayerCard stockEquivalent = null;
                    foreach (var card in stockPlayers.GetComponentsInChildren<PlayerCard>())
                    {
                        if (card.character == null) continue;

                        if (card.character.id == draggedCard.character.id && card.charInStockButAlsoInActive)
                        {
                            stockEquivalent = card;
                            break;
                        }
                    }

                    //Make equivalent empty
                    configsHandler.SetCharacterToParty(partyIndex, stockEquivalent.partyCharacterIndex, stockEquivalent.isInActive, null);
                }
            }
            else if (draggedCard.character == null && targetCard.character != null)
            {
                if (draggedCard.isInActive)
                {
                    PlayerCard stockEquivalent = null;
                    foreach (var card in stockPlayers.GetComponentsInChildren<PlayerCard>())
                    {
                        if (card.character == null) continue;

                        if (card.character.id == targetCard.character.id && card.charInStockButAlsoInActive)
                        {
                            stockEquivalent = card;
                            break;
                        }
                    }

                    //Make equivalent empty
                    configsHandler.SetCharacterToParty(partyIndex, stockEquivalent.partyCharacterIndex, stockEquivalent.isInActive, null);
                }
                else
                {
                    configsHandler.SetCharacterToParty(partyIndex, draggedCard.partyCharacterIndex, targetCard.isInActive, targetCard.character);
                    draggedCard.charInStockButAlsoInActive = true;
                }
            }

            // Update UI after swapping indices
            UpdateAllCardsUI();

        }

        // Update names to reflect new positions
        draggedCard.gameObject.transform.parent.name = (draggedCard.isInActive ? "AP" : "SP") + draggedCard.partyCharacterIndex;
        targetCard.gameObject.transform.parent.name = (targetCard.isInActive ? "AP" : "SP") + targetCard.partyCharacterIndex;
    }

    // Update UI for all playerCards in the party
    public void UpdateAllCardsUI()
    {
        // Find all playerCard components in the party container
        PlayerCard[] allCards = transform.GetComponentsInChildren<PlayerCard>();

        foreach (PlayerCard card in allCards)
        {
            if (card != null)
            {
                card.UpdateUI();
            }
        }
    }


    void PartyNameEditted(string name, TMP_InputField text) // updates the party name
    {
        configsHandler.SetPartyName(partyIndex, name);
        configsHandler.SaveParties();
    }
}
