{"Version": 1, "WorkspaceRootPath": "D:\\Menino Autista\\DKG-RPG\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{BDAE39B7-E7BC-E4ED-FF3A-F1C69EB08582}|Assembly-CSharp.csproj|D:\\Menino Autista\\DKG-RPG\\assets\\resources\\scripts\\configs\\statsvaluedisplay.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{BDAE39B7-E7BC-E4ED-FF3A-F1C69EB08582}|Assembly-CSharp.csproj|solutionrelative:assets\\resources\\scripts\\configs\\statsvaluedisplay.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{BDAE39B7-E7BC-E4ED-FF3A-F1C69EB08582}|Assembly-CSharp.csproj|D:\\Menino Autista\\DKG-RPG\\assets\\resources\\scripts\\configs\\valuesscroll.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{BDAE39B7-E7BC-E4ED-FF3A-F1C69EB08582}|Assembly-CSharp.csproj|solutionrelative:assets\\resources\\scripts\\configs\\valuesscroll.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{BDAE39B7-E7BC-E4ED-FF3A-F1C69EB08582}|Assembly-CSharp.csproj|D:\\Menino Autista\\DKG-RPG\\assets\\resources\\scripts\\ui\\actionmenu.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{BDAE39B7-E7BC-E4ED-FF3A-F1C69EB08582}|Assembly-CSharp.csproj|solutionrelative:assets\\resources\\scripts\\ui\\actionmenu.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{BDAE39B7-E7BC-E4ED-FF3A-F1C69EB08582}|Assembly-CSharp.csproj|D:\\Menino Autista\\DKG-RPG\\assets\\resources\\scripts\\ui\\enemyvalues.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{BDAE39B7-E7BC-E4ED-FF3A-F1C69EB08582}|Assembly-CSharp.csproj|solutionrelative:assets\\resources\\scripts\\ui\\enemyvalues.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{BDAE39B7-E7BC-E4ED-FF3A-F1C69EB08582}|Assembly-CSharp.csproj|d:\\menino autista\\dkg-rpg\\assets\\resources\\scripts\\tests\\touch tests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{BDAE39B7-E7BC-E4ED-FF3A-F1C69EB08582}|Assembly-CSharp.csproj|solutionrelative:assets\\resources\\scripts\\tests\\touch tests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{BDAE39B7-E7BC-E4ED-FF3A-F1C69EB08582}|Assembly-CSharp.csproj|D:\\Menino Autista\\DKG-RPG\\assets\\resources\\scripts\\configs\\skillsvaluedisplay.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{BDAE39B7-E7BC-E4ED-FF3A-F1C69EB08582}|Assembly-CSharp.csproj|solutionrelative:assets\\resources\\scripts\\configs\\skillsvaluedisplay.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{BDAE39B7-E7BC-E4ED-FF3A-F1C69EB08582}|Assembly-CSharp.csproj|D:\\Menino Autista\\DKG-RPG\\assets\\resources\\scripts\\fpslocker\\fpslocker.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{BDAE39B7-E7BC-E4ED-FF3A-F1C69EB08582}|Assembly-CSharp.csproj|solutionrelative:assets\\resources\\scripts\\fpslocker\\fpslocker.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{BDAE39B7-E7BC-E4ED-FF3A-F1C69EB08582}|Assembly-CSharp.csproj|D:\\Menino Autista\\DKG-RPG\\assets\\resources\\scripts\\configs\\addcharbutton.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{BDAE39B7-E7BC-E4ED-FF3A-F1C69EB08582}|Assembly-CSharp.csproj|solutionrelative:assets\\resources\\scripts\\configs\\addcharbutton.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{BDAE39B7-E7BC-E4ED-FF3A-F1C69EB08582}|Assembly-CSharp.csproj|D:\\Menino Autista\\DKG-RPG\\assets\\resources\\scripts\\ui\\addbuffndebuff.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{BDAE39B7-E7BC-E4ED-FF3A-F1C69EB08582}|Assembly-CSharp.csproj|solutionrelative:assets\\resources\\scripts\\ui\\addbuffndebuff.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{BDAE39B7-E7BC-E4ED-FF3A-F1C69EB08582}|Assembly-CSharp.csproj|D:\\Menino Autista\\DKG-RPG\\assets\\resources\\scripts\\characters\\addplayer.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{BDAE39B7-E7BC-E4ED-FF3A-F1C69EB08582}|Assembly-CSharp.csproj|solutionrelative:assets\\resources\\scripts\\characters\\addplayer.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{BDAE39B7-E7BC-E4ED-FF3A-F1C69EB08582}|Assembly-CSharp.csproj|D:\\Menino Autista\\DKG-RPG\\assets\\resources\\scripts\\configs\\buffndebuffs.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{BDAE39B7-E7BC-E4ED-FF3A-F1C69EB08582}|Assembly-CSharp.csproj|solutionrelative:assets\\resources\\scripts\\configs\\buffndebuffs.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{BDAE39B7-E7BC-E4ED-FF3A-F1C69EB08582}|Assembly-CSharp.csproj|D:\\Menino Autista\\DKG-RPG\\assets\\resources\\scripts\\ui\\buffconfui.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{BDAE39B7-E7BC-E4ED-FF3A-F1C69EB08582}|Assembly-CSharp.csproj|solutionrelative:assets\\resources\\scripts\\ui\\buffconfui.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{BDAE39B7-E7BC-E4ED-FF3A-F1C69EB08582}|Assembly-CSharp.csproj|D:\\Menino Autista\\DKG-RPG\\assets\\resources\\scripts\\configs\\pastebutton.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{BDAE39B7-E7BC-E4ED-FF3A-F1C69EB08582}|Assembly-CSharp.csproj|solutionrelative:assets\\resources\\scripts\\configs\\pastebutton.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{BDAE39B7-E7BC-E4ED-FF3A-F1C69EB08582}|Assembly-CSharp.csproj|D:\\Menino Autista\\DKG-RPG\\assets\\resources\\scripts\\configs\\modsvaluedisplay.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{BDAE39B7-E7BC-E4ED-FF3A-F1C69EB08582}|Assembly-CSharp.csproj|solutionrelative:assets\\resources\\scripts\\configs\\modsvaluedisplay.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{BDAE39B7-E7BC-E4ED-FF3A-F1C69EB08582}|Assembly-CSharp.csproj|D:\\Menino Autista\\DKG-RPG\\assets\\resources\\scripts\\ui\\addcharacter.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{BDAE39B7-E7BC-E4ED-FF3A-F1C69EB08582}|Assembly-CSharp.csproj|solutionrelative:assets\\resources\\scripts\\ui\\addcharacter.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{BDAE39B7-E7BC-E4ED-FF3A-F1C69EB08582}|Assembly-CSharp.csproj|D:\\Menino Autista\\DKG-RPG\\assets\\resources\\scripts\\ui\\editlettervalues.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{BDAE39B7-E7BC-E4ED-FF3A-F1C69EB08582}|Assembly-CSharp.csproj|solutionrelative:assets\\resources\\scripts\\ui\\editlettervalues.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{BDAE39B7-E7BC-E4ED-FF3A-F1C69EB08582}|Assembly-CSharp.csproj|D:\\Menino Autista\\DKG-RPG\\assets\\resources\\scripts\\ui\\damagelabel.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{BDAE39B7-E7BC-E4ED-FF3A-F1C69EB08582}|Assembly-CSharp.csproj|solutionrelative:assets\\resources\\scripts\\ui\\damagelabel.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{BDAE39B7-E7BC-E4ED-FF3A-F1C69EB08582}|Assembly-CSharp.csproj|D:\\Menino Autista\\DKG-RPG\\assets\\resources\\scripts\\types.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{BDAE39B7-E7BC-E4ED-FF3A-F1C69EB08582}|Assembly-CSharp.csproj|solutionrelative:assets\\resources\\scripts\\types.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{BDAE39B7-E7BC-E4ED-FF3A-F1C69EB08582}|Assembly-CSharp.csproj|D:\\Menino Autista\\DKG-RPG\\assets\\resources\\scripts\\characters\\characterskills.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{BDAE39B7-E7BC-E4ED-FF3A-F1C69EB08582}|Assembly-CSharp.csproj|solutionrelative:assets\\resources\\scripts\\characters\\characterskills.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{BDAE39B7-E7BC-E4ED-FF3A-F1C69EB08582}|Assembly-CSharp.csproj|D:\\Menino Autista\\DKG-RPG\\assets\\resources\\scripts\\characters\\charactermods.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{BDAE39B7-E7BC-E4ED-FF3A-F1C69EB08582}|Assembly-CSharp.csproj|solutionrelative:assets\\resources\\scripts\\characters\\charactermods.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{BDAE39B7-E7BC-E4ED-FF3A-F1C69EB08582}|Assembly-CSharp.csproj|D:\\Menino Autista\\DKG-RPG\\assets\\resources\\scripts\\configs\\switchcameras.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{BDAE39B7-E7BC-E4ED-FF3A-F1C69EB08582}|Assembly-CSharp.csproj|solutionrelative:assets\\resources\\scripts\\configs\\switchcameras.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{BDAE39B7-E7BC-E4ED-FF3A-F1C69EB08582}|Assembly-CSharp.csproj|D:\\Menino Autista\\DKG-RPG\\assets\\resources\\scripts\\ui\\enemyinterface.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{BDAE39B7-E7BC-E4ED-FF3A-F1C69EB08582}|Assembly-CSharp.csproj|solutionrelative:assets\\resources\\scripts\\ui\\enemyinterface.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{BDAE39B7-E7BC-E4ED-FF3A-F1C69EB08582}|Assembly-CSharp.csproj|D:\\Menino Autista\\DKG-RPG\\assets\\resources\\scripts\\minigame\\grid.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{BDAE39B7-E7BC-E4ED-FF3A-F1C69EB08582}|Assembly-CSharp.csproj|solutionrelative:assets\\resources\\scripts\\minigame\\grid.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{BDAE39B7-E7BC-E4ED-FF3A-F1C69EB08582}|Assembly-CSharp.csproj|D:\\Menino Autista\\DKG-RPG\\assets\\resources\\scripts\\minigame\\gridcreator.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{BDAE39B7-E7BC-E4ED-FF3A-F1C69EB08582}|Assembly-CSharp.csproj|solutionrelative:assets\\resources\\scripts\\minigame\\gridcreator.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{BDAE39B7-E7BC-E4ED-FF3A-F1C69EB08582}|Assembly-CSharp.csproj|D:\\Menino Autista\\DKG-RPG\\assets\\resources\\scripts\\ui\\editenergytime.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{BDAE39B7-E7BC-E4ED-FF3A-F1C69EB08582}|Assembly-CSharp.csproj|solutionrelative:assets\\resources\\scripts\\ui\\editenergytime.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{BDAE39B7-E7BC-E4ED-FF3A-F1C69EB08582}|Assembly-CSharp.csproj|D:\\Menino Autista\\DKG-RPG\\assets\\resources\\scripts\\minigame\\tile.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{BDAE39B7-E7BC-E4ED-FF3A-F1C69EB08582}|Assembly-CSharp.csproj|solutionrelative:assets\\resources\\scripts\\minigame\\tile.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{BDAE39B7-E7BC-E4ED-FF3A-F1C69EB08582}|Assembly-CSharp.csproj|D:\\Menino Autista\\DKG-RPG\\assets\\resources\\scripts\\ui\\playerinterface.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{BDAE39B7-E7BC-E4ED-FF3A-F1C69EB08582}|Assembly-CSharp.csproj|solutionrelative:assets\\resources\\scripts\\ui\\playerinterface.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{BDAE39B7-E7BC-E4ED-FF3A-F1C69EB08582}|Assembly-CSharp.csproj|D:\\Menino Autista\\DKG-RPG\\assets\\resources\\scripts\\fpslocker\\fpscounter.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{BDAE39B7-E7BC-E4ED-FF3A-F1C69EB08582}|Assembly-CSharp.csproj|solutionrelative:assets\\resources\\scripts\\fpslocker\\fpscounter.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{BDAE39B7-E7BC-E4ED-FF3A-F1C69EB08582}|Assembly-CSharp.csproj|D:\\Menino Autista\\DKG-RPG\\assets\\resources\\scripts\\minigame\\tilecollisionhandler.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{BDAE39B7-E7BC-E4ED-FF3A-F1C69EB08582}|Assembly-CSharp.csproj|solutionrelative:assets\\resources\\scripts\\minigame\\tilecollisionhandler.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{BDAE39B7-E7BC-E4ED-FF3A-F1C69EB08582}|Assembly-CSharp.csproj|D:\\Menino Autista\\DKG-RPG\\assets\\resources\\scripts\\configs\\loadvalues.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{BDAE39B7-E7BC-E4ED-FF3A-F1C69EB08582}|Assembly-CSharp.csproj|solutionrelative:assets\\resources\\scripts\\configs\\loadvalues.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{BDAE39B7-E7BC-E4ED-FF3A-F1C69EB08582}|Assembly-CSharp.csproj|D:\\Menino Autista\\DKG-RPG\\assets\\resources\\scripts\\ui\\skilliconupdate.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{BDAE39B7-E7BC-E4ED-FF3A-F1C69EB08582}|Assembly-CSharp.csproj|solutionrelative:assets\\resources\\scripts\\ui\\skilliconupdate.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{BDAE39B7-E7BC-E4ED-FF3A-F1C69EB08582}|Assembly-CSharp.csproj|D:\\Menino Autista\\DKG-RPG\\assets\\resources\\scripts\\ui\\turnsui.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{BDAE39B7-E7BC-E4ED-FF3A-F1C69EB08582}|Assembly-CSharp.csproj|solutionrelative:assets\\resources\\scripts\\ui\\turnsui.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{BDAE39B7-E7BC-E4ED-FF3A-F1C69EB08582}|Assembly-CSharp.csproj|D:\\Menino Autista\\DKG-RPG\\assets\\resources\\scripts\\configs\\configshandler.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{BDAE39B7-E7BC-E4ED-FF3A-F1C69EB08582}|Assembly-CSharp.csproj|solutionrelative:assets\\resources\\scripts\\configs\\configshandler.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{BDAE39B7-E7BC-E4ED-FF3A-F1C69EB08582}|Assembly-CSharp.csproj|D:\\Menino Autista\\DKG-RPG\\assets\\resources\\scripts\\configs\\combochance.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{BDAE39B7-E7BC-E4ED-FF3A-F1C69EB08582}|Assembly-CSharp.csproj|solutionrelative:assets\\resources\\scripts\\configs\\combochance.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{BDAE39B7-E7BC-E4ED-FF3A-F1C69EB08582}|Assembly-CSharp.csproj|D:\\Menino Autista\\DKG-RPG\\assets\\resources\\scripts\\characters\\battlecharacter.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{BDAE39B7-E7BC-E4ED-FF3A-F1C69EB08582}|Assembly-CSharp.csproj|solutionrelative:assets\\resources\\scripts\\characters\\battlecharacter.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{BDAE39B7-E7BC-E4ED-FF3A-F1C69EB08582}|Assembly-CSharp.csproj|D:\\Menino Autista\\DKG-RPG\\assets\\resources\\scripts\\characters\\characterstatus.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{BDAE39B7-E7BC-E4ED-FF3A-F1C69EB08582}|Assembly-CSharp.csproj|solutionrelative:assets\\resources\\scripts\\characters\\characterstatus.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{BDAE39B7-E7BC-E4ED-FF3A-F1C69EB08582}|Assembly-CSharp.csproj|D:\\Menino Autista\\DKG-RPG\\assets\\resources\\scripts\\ui\\canvasscalematch.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{BDAE39B7-E7BC-E4ED-FF3A-F1C69EB08582}|Assembly-CSharp.csproj|solutionrelative:assets\\resources\\scripts\\ui\\canvasscalematch.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{BDAE39B7-E7BC-E4ED-FF3A-F1C69EB08582}|Assembly-CSharp.csproj|D:\\Menino Autista\\DKG-RPG\\assets\\resources\\scripts\\minigame\\attackeffect.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{BDAE39B7-E7BC-E4ED-FF3A-F1C69EB08582}|Assembly-CSharp.csproj|solutionrelative:assets\\resources\\scripts\\minigame\\attackeffect.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{BDAE39B7-E7BC-E4ED-FF3A-F1C69EB08582}|Assembly-CSharp.csproj|D:\\Menino Autista\\DKG-RPG\\assets\\resources\\scripts\\ui\\charconfui.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{BDAE39B7-E7BC-E4ED-FF3A-F1C69EB08582}|Assembly-CSharp.csproj|solutionrelative:assets\\resources\\scripts\\ui\\charconfui.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{BDAE39B7-E7BC-E4ED-FF3A-F1C69EB08582}|Assembly-CSharp.csproj|D:\\Menino Autista\\DKG-RPG\\assets\\resources\\scripts\\ui\\escapepopup.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{BDAE39B7-E7BC-E4ED-FF3A-F1C69EB08582}|Assembly-CSharp.csproj|solutionrelative:assets\\resources\\scripts\\ui\\escapepopup.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{BDAE39B7-E7BC-E4ED-FF3A-F1C69EB08582}|Assembly-CSharp.csproj|D:\\Menino Autista\\DKG-RPG\\assets\\resources\\scripts\\characters\\enemyanimation.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{BDAE39B7-E7BC-E4ED-FF3A-F1C69EB08582}|Assembly-CSharp.csproj|solutionrelative:assets\\resources\\scripts\\characters\\enemyanimation.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{BDAE39B7-E7BC-E4ED-FF3A-F1C69EB08582}|Assembly-CSharp.csproj|D:\\Menino Autista\\DKG-RPG\\assets\\resources\\scripts\\minigame\\tiledestroy.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{BDAE39B7-E7BC-E4ED-FF3A-F1C69EB08582}|Assembly-CSharp.csproj|solutionrelative:assets\\resources\\scripts\\minigame\\tiledestroy.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{BDAE39B7-E7BC-E4ED-FF3A-F1C69EB08582}|Assembly-CSharp.csproj|D:\\Menino Autista\\DKG-RPG\\assets\\resources\\scripts\\ui\\escapefailpopup.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{BDAE39B7-E7BC-E4ED-FF3A-F1C69EB08582}|Assembly-CSharp.csproj|solutionrelative:assets\\resources\\scripts\\ui\\escapefailpopup.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{BDAE39B7-E7BC-E4ED-FF3A-F1C69EB08582}|Assembly-CSharp.csproj|D:\\Menino Autista\\DKG-RPG\\assets\\resources\\scripts\\configs\\addbuffbutton.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{BDAE39B7-E7BC-E4ED-FF3A-F1C69EB08582}|Assembly-CSharp.csproj|solutionrelative:assets\\resources\\scripts\\configs\\addbuffbutton.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{BDAE39B7-E7BC-E4ED-FF3A-F1C69EB08582}|Assembly-CSharp.csproj|D:\\Menino Autista\\DKG-RPG\\assets\\resources\\scripts\\ui\\skillpositioncorrection.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{BDAE39B7-E7BC-E4ED-FF3A-F1C69EB08582}|Assembly-CSharp.csproj|solutionrelative:assets\\resources\\scripts\\ui\\skillpositioncorrection.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{BDAE39B7-E7BC-E4ED-FF3A-F1C69EB08582}|Assembly-CSharp.csproj|D:\\Menino Autista\\DKG-RPG\\assets\\resources\\scripts\\ui\\score.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{BDAE39B7-E7BC-E4ED-FF3A-F1C69EB08582}|Assembly-CSharp.csproj|solutionrelative:assets\\resources\\scripts\\ui\\score.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{BDAE39B7-E7BC-E4ED-FF3A-F1C69EB08582}|Assembly-CSharp.csproj|D:\\Menino Autista\\DKG-RPG\\assets\\resources\\scripts\\ui\\changeselectedcolor.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{BDAE39B7-E7BC-E4ED-FF3A-F1C69EB08582}|Assembly-CSharp.csproj|solutionrelative:assets\\resources\\scripts\\ui\\changeselectedcolor.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{BDAE39B7-E7BC-E4ED-FF3A-F1C69EB08582}|Assembly-CSharp.csproj|D:\\Menino Autista\\DKG-RPG\\assets\\resources\\scripts\\ui\\energytimepb.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{BDAE39B7-E7BC-E4ED-FF3A-F1C69EB08582}|Assembly-CSharp.csproj|solutionrelative:assets\\resources\\scripts\\ui\\energytimepb.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{BDAE39B7-E7BC-E4ED-FF3A-F1C69EB08582}|Assembly-CSharp.csproj|D:\\Menino Autista\\DKG-RPG\\assets\\resources\\scripts\\configs\\switchconfigs.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{BDAE39B7-E7BC-E4ED-FF3A-F1C69EB08582}|Assembly-CSharp.csproj|solutionrelative:assets\\resources\\scripts\\configs\\switchconfigs.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|D:\\Menino Autista\\DKG-RPG\\Assets\\Resources\\Scripts\\Tests\\DamageStar.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|solutionrelative:Assets\\Resources\\Scripts\\Tests\\DamageStar.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|D:\\Menino Autista\\DKG-RPG\\Assets\\Resources\\Scripts\\Tests\\CameraScaler.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|solutionrelative:Assets\\Resources\\Scripts\\Tests\\CameraScaler.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}], "DocumentGroupContainers": [{"Orientation": 0, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedWidth": 214, "SelectedChildIndex": 40, "Children": [{"$type": "Document", "DocumentIndex": 29, "Title": "LoadValues.cs", "DocumentMoniker": "D:\\Menino Autista\\DKG-RPG\\Assets\\Resources\\Scripts\\Configs\\LoadValues.cs", "RelativeDocumentMoniker": "Assets\\Resources\\Scripts\\Configs\\LoadValues.cs", "ToolTip": "D:\\Menino Autista\\DKG-RPG\\Assets\\Resources\\Scripts\\Configs\\LoadValues.cs", "RelativeToolTip": "Assets\\Resources\\Scripts\\Configs\\LoadValues.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAACIAAAANAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-02-14T19:26:54.648Z", "IsPinned": true}, {"$type": "Document", "DocumentIndex": 32, "Title": "ConfigsHandler.cs", "DocumentMoniker": "D:\\Menino Autista\\DKG-RPG\\Assets\\Resources\\Scripts\\Configs\\ConfigsHandler.cs", "RelativeDocumentMoniker": "Assets\\Resources\\Scripts\\Configs\\ConfigsHandler.cs", "ToolTip": "D:\\Menino Autista\\DKG-RPG\\Assets\\Resources\\Scripts\\Configs\\ConfigsHandler.cs", "RelativeToolTip": "Assets\\Resources\\Scripts\\Configs\\ConfigsHandler.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAoAAAARAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-02-06T15:57:07.648Z", "IsPinned": true}, {"$type": "Document", "DocumentIndex": 35, "Title": "CharacterStatus.cs", "DocumentMoniker": "D:\\Menino Autista\\DKG-RPG\\Assets\\Resources\\Scripts\\Characters\\CharacterStatus.cs", "RelativeDocumentMoniker": "Assets\\Resources\\Scripts\\Characters\\CharacterStatus.cs", "ToolTip": "D:\\Menino Autista\\DKG-RPG\\Assets\\Resources\\Scripts\\Characters\\CharacterStatus.cs", "RelativeToolTip": "Assets\\Resources\\Scripts\\Characters\\CharacterStatus.cs", "ViewState": "AgIAAAsAAAAAAAAAAAAQwC4AAABmAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-02-14T18:19:36.817Z", "IsPinned": true}, {"$type": "Document", "DocumentIndex": 34, "Title": "BattleCharacter.cs", "DocumentMoniker": "D:\\Menino Autista\\DKG-RPG\\Assets\\Resources\\Scripts\\Characters\\BattleCharacter.cs", "RelativeDocumentMoniker": "Assets\\Resources\\Scripts\\Characters\\BattleCharacter.cs", "ToolTip": "D:\\Menino Autista\\DKG-RPG\\Assets\\Resources\\Scripts\\Characters\\BattleCharacter.cs", "RelativeToolTip": "Assets\\Resources\\Scripts\\Characters\\BattleCharacter.cs", "ViewState": "AgIAAFQAAAAAAAAAAAAwwIMAAAAiAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-02-11T13:42:46.911Z", "IsPinned": true}, {"$type": "Document", "DocumentIndex": 26, "Title": "PlayerInterface.cs", "DocumentMoniker": "D:\\Menino Autista\\DKG-RPG\\Assets\\Resources\\Scripts\\UI\\PlayerInterface.cs", "RelativeDocumentMoniker": "Assets\\Resources\\Scripts\\UI\\PlayerInterface.cs", "ToolTip": "D:\\Menino Autista\\DKG-RPG\\Assets\\Resources\\Scripts\\UI\\PlayerInterface.cs", "RelativeToolTip": "Assets\\Resources\\Scripts\\UI\\PlayerInterface.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAADQAAAA6AAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-02-17T11:27:53.996Z", "IsPinned": true}, {"$type": "Bookmark", "Name": "ST:0:0:{aa2115a1-9712-457b-9047-dbb71ca2cdd2}"}, {"$type": "Document", "DocumentIndex": 4, "Title": "touch Tests.cs", "DocumentMoniker": "D:\\Menino Autista\\DKG-RPG\\Assets\\Resources\\Scripts\\Tests\\touch Tests.cs", "RelativeDocumentMoniker": "Assets\\Resources\\Scripts\\Tests\\touch Tests.cs", "ToolTip": "D:\\Menino Autista\\DKG-RPG\\Assets\\Resources\\Scripts\\Tests\\touch Tests.cs", "RelativeToolTip": "Assets\\Resources\\Scripts\\Tests\\touch Tests.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAABUAAABmAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-02-28T11:28:49.96Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 27, "Title": "FPSCounter.cs", "DocumentMoniker": "D:\\Menino Autista\\DKG-RPG\\Assets\\Resources\\Scripts\\FPSLocker\\FPSCounter.cs", "RelativeDocumentMoniker": "Assets\\Resources\\Scripts\\FPSLocker\\FPSCounter.cs", "ToolTip": "D:\\Menino Autista\\DKG-RPG\\Assets\\Resources\\Scripts\\FPSLocker\\FPSCounter.cs", "RelativeToolTip": "Assets\\Resources\\Scripts\\FPSLocker\\FPSCounter.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAABkAAAAsAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-02-28T11:06:07.325Z"}, {"$type": "Document", "DocumentIndex": 37, "Title": "AttackEffect.cs", "DocumentMoniker": "D:\\Menino Autista\\DKG-RPG\\Assets\\Resources\\Scripts\\MiniGame\\AttackEffect.cs", "RelativeDocumentMoniker": "Assets\\Resources\\Scripts\\MiniGame\\AttackEffect.cs", "ToolTip": "D:\\Menino Autista\\DKG-RPG\\Assets\\Resources\\Scripts\\MiniGame\\AttackEffect.cs", "RelativeToolTip": "Assets\\Resources\\Scripts\\MiniGame\\AttackEffect.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAABAAAAAIAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-02-26T14:50:00.678Z"}, {"$type": "Document", "DocumentIndex": 49, "Title": "DamageStar.cs", "DocumentMoniker": "D:\\Menino Autista\\DKG-RPG\\Assets\\Resources\\Scripts\\Tests\\DamageStar.cs", "RelativeDocumentMoniker": "Assets\\Resources\\Scripts\\Tests\\DamageStar.cs", "ToolTip": "D:\\Menino Autista\\DKG-RPG\\Assets\\Resources\\Scripts\\Tests\\DamageStar.cs", "RelativeToolTip": "Assets\\Resources\\Scripts\\Tests\\DamageStar.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAACMAAAAgAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-02-26T13:46:34.675Z"}, {"$type": "Document", "DocumentIndex": 50, "Title": "CameraScaler.cs", "DocumentMoniker": "D:\\Menino Autista\\DKG-RPG\\Assets\\Resources\\Scripts\\Tests\\CameraScaler.cs", "RelativeDocumentMoniker": "Assets\\Resources\\Scripts\\Tests\\CameraScaler.cs", "ToolTip": "D:\\Menino Autista\\DKG-RPG\\Assets\\Resources\\Scripts\\Tests\\CameraScaler.cs", "RelativeToolTip": "Assets\\Resources\\Scripts\\Tests\\CameraScaler.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAMAAAABAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-02-25T15:13:11.103Z"}, {"$type": "Document", "DocumentIndex": 36, "Title": "CanvasScaleMatch.cs", "DocumentMoniker": "D:\\Menino Autista\\DKG-RPG\\Assets\\Resources\\Scripts\\UI\\CanvasScaleMatch.cs", "RelativeDocumentMoniker": "Assets\\Resources\\Scripts\\UI\\CanvasScaleMatch.cs", "ToolTip": "D:\\Menino Autista\\DKG-RPG\\Assets\\Resources\\Scripts\\UI\\CanvasScaleMatch.cs", "RelativeToolTip": "Assets\\Resources\\Scripts\\UI\\CanvasScaleMatch.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAUAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-02-25T15:05:42.615Z"}, {"$type": "Document", "DocumentIndex": 46, "Title": "ChangeSelectedColor.cs", "DocumentMoniker": "D:\\Menino Autista\\DKG-RPG\\Assets\\Resources\\Scripts\\UI\\ChangeSelectedColor.cs", "RelativeDocumentMoniker": "Assets\\Resources\\Scripts\\UI\\ChangeSelectedColor.cs", "ToolTip": "D:\\Menino Autista\\DKG-RPG\\Assets\\Resources\\Scripts\\UI\\ChangeSelectedColor.cs", "RelativeToolTip": "Assets\\Resources\\Scripts\\UI\\ChangeSelectedColor.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-02-25T12:48:14.22Z"}, {"$type": "Document", "DocumentIndex": 45, "Title": "Score.cs", "DocumentMoniker": "D:\\Menino Autista\\DKG-RPG\\Assets\\Resources\\Scripts\\UI\\Score.cs", "RelativeDocumentMoniker": "Assets\\Resources\\Scripts\\UI\\Score.cs", "ToolTip": "D:\\Menino Autista\\DKG-RPG\\Assets\\Resources\\Scripts\\UI\\Score.cs", "RelativeToolTip": "Assets\\Resources\\Scripts\\UI\\Score.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAMAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-02-25T11:15:55.567Z"}, {"$type": "Document", "DocumentIndex": 40, "Title": "EnemyAnimation.cs", "DocumentMoniker": "D:\\Menino Autista\\DKG-RPG\\Assets\\Resources\\Scripts\\Characters\\EnemyAnimation.cs", "RelativeDocumentMoniker": "Assets\\Resources\\Scripts\\Characters\\EnemyAnimation.cs", "ToolTip": "D:\\Menino Autista\\DKG-RPG\\Assets\\Resources\\Scripts\\Characters\\EnemyAnimation.cs", "RelativeToolTip": "Assets\\Resources\\Scripts\\Characters\\EnemyAnimation.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAACAAAAAjAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-02-25T11:13:24.29Z"}, {"$type": "Document", "DocumentIndex": 6, "Title": "FPSLocker.cs", "DocumentMoniker": "D:\\Menino Autista\\DKG-RPG\\Assets\\Resources\\Scripts\\FPSLocker\\FPSLocker.cs", "RelativeDocumentMoniker": "Assets\\Resources\\Scripts\\FPSLocker\\FPSLocker.cs", "ToolTip": "D:\\Menino Autista\\DKG-RPG\\Assets\\Resources\\Scripts\\FPSLocker\\FPSLocker.cs", "RelativeToolTip": "Assets\\Resources\\Scripts\\FPSLocker\\FPSLocker.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAB4AAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-02-24T17:14:39.407Z"}, {"$type": "Document", "DocumentIndex": 41, "Title": "TIleDestroy.cs", "DocumentMoniker": "D:\\Menino Autista\\DKG-RPG\\Assets\\Resources\\Scripts\\MiniGame\\TIleDestroy.cs", "RelativeDocumentMoniker": "Assets\\Resources\\Scripts\\MiniGame\\TIleDestroy.cs", "ToolTip": "D:\\Menino Autista\\DKG-RPG\\Assets\\Resources\\Scripts\\MiniGame\\TIleDestroy.cs", "RelativeToolTip": "Assets\\Resources\\Scripts\\MiniGame\\TIleDestroy.cs", "ViewState": "AgIAAAkAAAAAAAAAAAAcwBsAAAAgAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-02-21T18:04:46.34Z"}, {"$type": "Document", "DocumentIndex": 30, "Title": "SkillIconUpdate.cs", "DocumentMoniker": "D:\\Menino Autista\\DKG-RPG\\Assets\\Resources\\Scripts\\UI\\SkillIconUpdate.cs", "RelativeDocumentMoniker": "Assets\\Resources\\Scripts\\UI\\SkillIconUpdate.cs", "ToolTip": "D:\\Menino Autista\\DKG-RPG\\Assets\\Resources\\Scripts\\UI\\SkillIconUpdate.cs", "RelativeToolTip": "Assets\\Resources\\Scripts\\UI\\SkillIconUpdate.cs", "ViewState": "AgIAACQAAAAAAAAAAAAowDoAAAAyAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-02-21T15:04:32.456Z"}, {"$type": "Document", "DocumentIndex": 31, "Title": "TurnsUI.cs", "DocumentMoniker": "D:\\Menino Autista\\DKG-RPG\\Assets\\Resources\\Scripts\\UI\\TurnsUI.cs", "RelativeDocumentMoniker": "Assets\\Resources\\Scripts\\UI\\TurnsUI.cs", "ToolTip": "D:\\Menino Autista\\DKG-RPG\\Assets\\Resources\\Scripts\\UI\\TurnsUI.cs", "RelativeToolTip": "Assets\\Resources\\Scripts\\UI\\TurnsUI.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAA0AAAATAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-02-21T11:23:19.872Z"}, {"$type": "Document", "DocumentIndex": 48, "Title": "SwitchConfigs.cs", "DocumentMoniker": "D:\\Menino Autista\\DKG-RPG\\Assets\\Resources\\Scripts\\Configs\\SwitchConfigs.cs", "RelativeDocumentMoniker": "Assets\\Resources\\Scripts\\Configs\\SwitchConfigs.cs", "ToolTip": "D:\\Menino Autista\\DKG-RPG\\Assets\\Resources\\Scripts\\Configs\\SwitchConfigs.cs", "RelativeToolTip": "Assets\\Resources\\Scripts\\Configs\\SwitchConfigs.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-02-20T17:32:27.771Z"}, {"$type": "Document", "DocumentIndex": 33, "Title": "ComboChance.cs", "DocumentMoniker": "D:\\Menino Autista\\DKG-RPG\\Assets\\Resources\\Scripts\\Configs\\ComboChance.cs", "RelativeDocumentMoniker": "Assets\\Resources\\Scripts\\Configs\\ComboChance.cs", "ToolTip": "D:\\Menino Autista\\DKG-RPG\\Assets\\Resources\\Scripts\\Configs\\ComboChance.cs", "RelativeToolTip": "Assets\\Resources\\Scripts\\Configs\\ComboChance.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAABoAAABMAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-02-20T17:18:47.464Z"}, {"$type": "Document", "DocumentIndex": 47, "Title": "EnergyTimePB.cs", "DocumentMoniker": "D:\\Menino Autista\\DKG-RPG\\Assets\\Resources\\Scripts\\UI\\EnergyTimePB.cs", "RelativeDocumentMoniker": "Assets\\Resources\\Scripts\\UI\\EnergyTimePB.cs", "ToolTip": "D:\\Menino Autista\\DKG-RPG\\Assets\\Resources\\Scripts\\UI\\EnergyTimePB.cs", "RelativeToolTip": "Assets\\Resources\\Scripts\\UI\\EnergyTimePB.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAABMAAAA9AAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-02-20T15:38:48.305Z"}, {"$type": "Document", "DocumentIndex": 28, "Title": "TileCollisionHandler.cs", "DocumentMoniker": "D:\\Menino Autista\\DKG-RPG\\Assets\\Resources\\Scripts\\MiniGame\\TileCollisionHandler.cs", "RelativeDocumentMoniker": "Assets\\Resources\\Scripts\\MiniGame\\TileCollisionHandler.cs", "ToolTip": "D:\\Menino Autista\\DKG-RPG\\Assets\\Resources\\Scripts\\MiniGame\\TileCollisionHandler.cs", "RelativeToolTip": "Assets\\Resources\\Scripts\\MiniGame\\TileCollisionHandler.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAA4AAABrAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-02-20T12:42:31.038Z"}, {"$type": "Document", "DocumentIndex": 42, "Title": "EscapeFailPopup.cs", "DocumentMoniker": "D:\\Menino Autista\\DKG-RPG\\Assets\\Resources\\Scripts\\UI\\EscapeFailPopup.cs", "RelativeDocumentMoniker": "Assets\\Resources\\Scripts\\UI\\EscapeFailPopup.cs", "ToolTip": "D:\\Menino Autista\\DKG-RPG\\Assets\\Resources\\Scripts\\UI\\EscapeFailPopup.cs", "RelativeToolTip": "Assets\\Resources\\Scripts\\UI\\EscapeFailPopup.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAsAAAATAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-02-20T11:09:00.166Z"}, {"$type": "Document", "DocumentIndex": 39, "Title": "EscapePopup.cs", "DocumentMoniker": "D:\\Menino Autista\\DKG-RPG\\Assets\\Resources\\Scripts\\UI\\EscapePopup.cs", "RelativeDocumentMoniker": "Assets\\Resources\\Scripts\\UI\\EscapePopup.cs", "ToolTip": "D:\\Menino Autista\\DKG-RPG\\Assets\\Resources\\Scripts\\UI\\EscapePopup.cs", "RelativeToolTip": "Assets\\Resources\\Scripts\\UI\\EscapePopup.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAABEAAAAzAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-02-20T10:21:47.105Z"}, {"$type": "Document", "DocumentIndex": 38, "Title": "CharConfUI.cs", "DocumentMoniker": "D:\\Menino Autista\\DKG-RPG\\Assets\\Resources\\Scripts\\UI\\CharConfUI.cs", "RelativeDocumentMoniker": "Assets\\Resources\\Scripts\\UI\\CharConfUI.cs", "ToolTip": "D:\\Menino Autista\\DKG-RPG\\Assets\\Resources\\Scripts\\UI\\CharConfUI.cs", "RelativeToolTip": "Assets\\Resources\\Scripts\\UI\\CharConfUI.cs", "ViewState": "AgIAAHUAAAAAAAAAAAAowI8AAACQAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-02-19T15:35:14.064Z"}, {"$type": "Document", "DocumentIndex": 44, "Title": "SkillPositionCorrection.cs", "DocumentMoniker": "D:\\Menino Autista\\DKG-RPG\\Assets\\Resources\\Scripts\\UI\\SkillPositionCorrection.cs", "RelativeDocumentMoniker": "Assets\\Resources\\Scripts\\UI\\SkillPositionCorrection.cs", "ToolTip": "D:\\Menino Autista\\DKG-RPG\\Assets\\Resources\\Scripts\\UI\\SkillPositionCorrection.cs", "RelativeToolTip": "Assets\\Resources\\Scripts\\UI\\SkillPositionCorrection.cs", "ViewState": "AgIAAAsAAAAAAAAAAAAQwDYAAAAeAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-02-19T14:30:23.104Z"}, {"$type": "Document", "DocumentIndex": 43, "Title": "AddBuffButton.cs", "DocumentMoniker": "D:\\Menino Autista\\DKG-RPG\\Assets\\Resources\\Scripts\\Configs\\AddBuffButton.cs", "RelativeDocumentMoniker": "Assets\\Resources\\Scripts\\Configs\\AddBuffButton.cs", "ToolTip": "D:\\Menino Autista\\DKG-RPG\\Assets\\Resources\\Scripts\\Configs\\AddBuffButton.cs", "RelativeToolTip": "Assets\\Resources\\Scripts\\Configs\\AddBuffButton.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAABIAAABYAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-02-19T13:21:27.083Z"}, {"$type": "Document", "DocumentIndex": 7, "Title": "AddCharButton.cs", "DocumentMoniker": "D:\\Menino Autista\\DKG-RPG\\Assets\\Resources\\scripts\\Configs\\AddCharButton.cs", "RelativeDocumentMoniker": "Assets\\Resources\\scripts\\Configs\\AddCharButton.cs", "ToolTip": "D:\\Menino Autista\\DKG-RPG\\Assets\\Resources\\scripts\\Configs\\AddCharButton.cs", "RelativeToolTip": "Assets\\Resources\\scripts\\Configs\\AddCharButton.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAABIAAABUAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-02-14T19:27:57.335Z"}, {"$type": "Document", "DocumentIndex": 8, "Title": "AddBuffNDeBuff.cs", "DocumentMoniker": "D:\\Menino Autista\\DKG-RPG\\Assets\\Resources\\Scripts\\UI\\AddBuffNDeBuff.cs", "RelativeDocumentMoniker": "Assets\\Resources\\Scripts\\UI\\AddBuffNDeBuff.cs", "ToolTip": "D:\\Menino Autista\\DKG-RPG\\Assets\\Resources\\Scripts\\UI\\AddBuffNDeBuff.cs", "RelativeToolTip": "Assets\\Resources\\Scripts\\UI\\AddBuffNDeBuff.cs", "ViewState": "AgIAAAUAAAAAAAAAAAAQwCkAAAA3AAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-02-19T12:05:50.159Z"}, {"$type": "Document", "DocumentIndex": 9, "Title": "AddPlayer.cs", "DocumentMoniker": "D:\\Menino Autista\\DKG-RPG\\Assets\\Resources\\Scripts\\Characters\\AddPlayer.cs", "RelativeDocumentMoniker": "Assets\\Resources\\Scripts\\Characters\\AddPlayer.cs", "ToolTip": "D:\\Menino Autista\\DKG-RPG\\Assets\\Resources\\Scripts\\Characters\\AddPlayer.cs", "RelativeToolTip": "Assets\\Resources\\Scripts\\Characters\\AddPlayer.cs", "ViewState": "AgIAAAMAAAAAAAAAAAAAACoAAABEAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-02-19T11:33:50.748Z"}, {"$type": "Document", "DocumentIndex": 10, "Title": "BuffNDeBuffs.cs", "DocumentMoniker": "D:\\Menino Autista\\DKG-RPG\\Assets\\Resources\\Scripts\\Configs\\BuffNDeBuffs.cs", "RelativeDocumentMoniker": "Assets\\Resources\\Scripts\\Configs\\BuffNDeBuffs.cs", "ToolTip": "D:\\Menino Autista\\DKG-RPG\\Assets\\Resources\\Scripts\\Configs\\BuffNDeBuffs.cs", "RelativeToolTip": "Assets\\Resources\\Scripts\\Configs\\BuffNDeBuffs.cs", "ViewState": "AgIAAAAAAAAAAAAAAAA6wAgAAAA+AAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-02-19T11:29:28.942Z"}, {"$type": "Document", "DocumentIndex": 11, "Title": "BuffConfUI.cs", "DocumentMoniker": "D:\\Menino Autista\\DKG-RPG\\Assets\\Resources\\Scripts\\UI\\BuffConfUI.cs", "RelativeDocumentMoniker": "Assets\\Resources\\Scripts\\UI\\BuffConfUI.cs", "ToolTip": "D:\\Menino Autista\\DKG-RPG\\Assets\\Resources\\Scripts\\UI\\BuffConfUI.cs", "RelativeToolTip": "Assets\\Resources\\Scripts\\UI\\BuffConfUI.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAADYAAAA8AAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-02-19T11:27:35.536Z"}, {"$type": "Document", "DocumentIndex": 2, "Title": "ActionMenu.cs", "DocumentMoniker": "D:\\Menino Autista\\DKG-RPG\\Assets\\Resources\\Scripts\\UI\\ActionMenu.cs", "RelativeDocumentMoniker": "Assets\\Resources\\Scripts\\UI\\ActionMenu.cs", "ToolTip": "D:\\Menino Autista\\DKG-RPG\\Assets\\Resources\\Scripts\\UI\\ActionMenu.cs", "RelativeToolTip": "Assets\\Resources\\Scripts\\UI\\ActionMenu.cs", "ViewState": "AgIAACIAAAAAAAAAAAAowDwAAAAIAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-02-18T17:09:17.863Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 3, "Title": "EnemyValues.cs", "DocumentMoniker": "D:\\Menino Autista\\DKG-RPG\\Assets\\Resources\\Scripts\\UI\\EnemyValues.cs", "RelativeDocumentMoniker": "Assets\\Resources\\Scripts\\UI\\EnemyValues.cs", "ToolTip": "D:\\Menino Autista\\DKG-RPG\\Assets\\Resources\\Scripts\\UI\\EnemyValues.cs", "RelativeToolTip": "Assets\\Resources\\Scripts\\UI\\EnemyValues.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAsAAABOAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-02-18T15:00:04.376Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 12, "Title": "PasteButton.cs", "DocumentMoniker": "D:\\Menino Autista\\DKG-RPG\\Assets\\Resources\\Scripts\\Configs\\PasteButton.cs", "RelativeDocumentMoniker": "Assets\\Resources\\Scripts\\Configs\\PasteButton.cs", "ToolTip": "D:\\Menino Autista\\DKG-RPG\\Assets\\Resources\\Scripts\\Configs\\PasteButton.cs", "RelativeToolTip": "Assets\\Resources\\Scripts\\Configs\\PasteButton.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAEAAAASAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-02-18T12:30:54.079Z"}, {"$type": "Document", "DocumentIndex": 1, "Title": "ValuesScroll.cs", "DocumentMoniker": "D:\\Menino Autista\\DKG-RPG\\Assets\\Resources\\Scripts\\Configs\\ValuesScroll.cs", "RelativeDocumentMoniker": "Assets\\Resources\\Scripts\\Configs\\ValuesScroll.cs", "ToolTip": "D:\\Menino Autista\\DKG-RPG\\Assets\\Resources\\Scripts\\Configs\\ValuesScroll.cs", "RelativeToolTip": "Assets\\Resources\\Scripts\\Configs\\ValuesScroll.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAACIAAAANAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-02-18T12:16:57.232Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 13, "Title": "ModsValueDisplay.cs", "DocumentMoniker": "D:\\Menino Autista\\DKG-RPG\\Assets\\Resources\\Scripts\\Configs\\ModsValueDisplay.cs", "RelativeDocumentMoniker": "Assets\\Resources\\Scripts\\Configs\\ModsValueDisplay.cs", "ToolTip": "D:\\Menino Autista\\DKG-RPG\\Assets\\Resources\\Scripts\\Configs\\ModsValueDisplay.cs", "RelativeToolTip": "Assets\\Resources\\Scripts\\Configs\\ModsValueDisplay.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAADkAAAAhAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-02-17T19:03:42.224Z"}, {"$type": "Document", "DocumentIndex": 5, "Title": "SkillsValueDisplay.cs", "DocumentMoniker": "D:\\Menino Autista\\DKG-RPG\\Assets\\Resources\\Scripts\\Configs\\SkillsValueDisplay.cs", "RelativeDocumentMoniker": "Assets\\Resources\\Scripts\\Configs\\SkillsValueDisplay.cs", "ToolTip": "D:\\Menino Autista\\DKG-RPG\\Assets\\Resources\\Scripts\\Configs\\SkillsValueDisplay.cs", "RelativeToolTip": "Assets\\Resources\\Scripts\\Configs\\SkillsValueDisplay.cs", "ViewState": "AgIAACgAAAAAAAAAAAAgwBIAAAA1AAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-02-17T18:02:42.965Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 14, "Title": "AddCharacter.cs", "DocumentMoniker": "D:\\Menino Autista\\DKG-RPG\\Assets\\Resources\\Scripts\\UI\\AddCharacter.cs", "RelativeDocumentMoniker": "Assets\\Resources\\Scripts\\UI\\AddCharacter.cs", "ToolTip": "D:\\Menino Autista\\DKG-RPG\\Assets\\Resources\\Scripts\\UI\\AddCharacter.cs", "RelativeToolTip": "Assets\\Resources\\Scripts\\UI\\AddCharacter.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAwAAAALAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-02-17T17:13:55.767Z"}, {"$type": "Document", "DocumentIndex": 0, "Title": "StatsValueDisplay.cs", "DocumentMoniker": "D:\\Menino Autista\\DKG-RPG\\Assets\\Resources\\Scripts\\Configs\\StatsValueDisplay.cs", "RelativeDocumentMoniker": "Assets\\Resources\\Scripts\\Configs\\StatsValueDisplay.cs", "ToolTip": "D:\\Menino Autista\\DKG-RPG\\Assets\\Resources\\Scripts\\Configs\\StatsValueDisplay.cs", "RelativeToolTip": "Assets\\Resources\\Scripts\\Configs\\StatsValueDisplay.cs", "ViewState": "AgIAABEAAAAAAAAAAAAQwDIAAAANAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-02-17T16:34:26.663Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 15, "Title": "EditLetterValues.cs", "DocumentMoniker": "D:\\Menino Autista\\DKG-RPG\\Assets\\Resources\\Scripts\\UI\\EditLetterValues.cs", "RelativeDocumentMoniker": "Assets\\Resources\\Scripts\\UI\\EditLetterValues.cs", "ToolTip": "D:\\Menino Autista\\DKG-RPG\\Assets\\Resources\\Scripts\\UI\\EditLetterValues.cs", "RelativeToolTip": "Assets\\Resources\\Scripts\\UI\\EditLetterValues.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAACgAAAAhAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-02-17T12:38:55.266Z"}, {"$type": "Document", "DocumentIndex": 24, "Title": "EditEnergyTime.cs", "DocumentMoniker": "D:\\Menino Autista\\DKG-RPG\\Assets\\Resources\\Scripts\\UI\\EditEnergyTime.cs", "RelativeDocumentMoniker": "Assets\\Resources\\Scripts\\UI\\EditEnergyTime.cs", "ToolTip": "D:\\Menino Autista\\DKG-RPG\\Assets\\Resources\\Scripts\\UI\\EditEnergyTime.cs", "RelativeToolTip": "Assets\\Resources\\Scripts\\UI\\EditEnergyTime.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAACEAAAAbAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-02-17T12:38:27.827Z"}, {"$type": "Document", "DocumentIndex": 25, "Title": "Tile.cs", "DocumentMoniker": "D:\\Menino Autista\\DKG-RPG\\Assets\\Resources\\Scripts\\MiniGame\\Tile.cs", "RelativeDocumentMoniker": "Assets\\Resources\\Scripts\\MiniGame\\Tile.cs", "ToolTip": "D:\\Menino Autista\\DKG-RPG\\Assets\\Resources\\Scripts\\MiniGame\\Tile.cs", "RelativeToolTip": "Assets\\Resources\\Scripts\\MiniGame\\Tile.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAC4AAAAXAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-02-17T11:46:56.741Z"}, {"$type": "Document", "DocumentIndex": 23, "Title": "GridCreator.cs", "DocumentMoniker": "D:\\Menino Autista\\DKG-RPG\\Assets\\Resources\\Scripts\\MiniGame\\GridCreator.cs", "RelativeDocumentMoniker": "Assets\\Resources\\Scripts\\MiniGame\\GridCreator.cs", "ToolTip": "D:\\Menino Autista\\DKG-RPG\\Assets\\Resources\\Scripts\\MiniGame\\GridCreator.cs", "RelativeToolTip": "Assets\\Resources\\Scripts\\MiniGame\\GridCreator.cs", "ViewState": "AgIAAFgAAAAAAAAAAAApwHYAAABDAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-02-17T11:36:28.589Z"}, {"$type": "Document", "DocumentIndex": 22, "Title": "Grid.cs", "DocumentMoniker": "D:\\Menino Autista\\DKG-RPG\\Assets\\Resources\\Scripts\\MiniGame\\Grid.cs", "RelativeDocumentMoniker": "Assets\\Resources\\Scripts\\MiniGame\\Grid.cs", "ToolTip": "D:\\Menino Autista\\DKG-RPG\\Assets\\Resources\\Scripts\\MiniGame\\Grid.cs", "RelativeToolTip": "Assets\\Resources\\Scripts\\MiniGame\\Grid.cs", "ViewState": "AgIAAAMAAAAAAAAAAAAAAEEAAAAQAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-02-17T11:31:15.527Z"}, {"$type": "Document", "DocumentIndex": 21, "Title": "EnemyInterface.cs", "DocumentMoniker": "D:\\Menino Autista\\DKG-RPG\\Assets\\Resources\\Scripts\\UI\\EnemyInterface.cs", "RelativeDocumentMoniker": "Assets\\Resources\\Scripts\\UI\\EnemyInterface.cs", "ToolTip": "D:\\Menino Autista\\DKG-RPG\\Assets\\Resources\\Scripts\\UI\\EnemyInterface.cs", "RelativeToolTip": "Assets\\Resources\\Scripts\\UI\\EnemyInterface.cs", "ViewState": "AgIAAAMAAAAAAAAAAAAAACMAAAAaAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-02-17T11:26:06.426Z"}, {"$type": "Document", "DocumentIndex": 20, "Title": "SwitchCameras.cs", "DocumentMoniker": "D:\\Menino Autista\\DKG-RPG\\Assets\\Resources\\scripts\\Configs\\SwitchCameras.cs", "RelativeDocumentMoniker": "Assets\\Resources\\scripts\\Configs\\SwitchCameras.cs", "ToolTip": "D:\\Menino Autista\\DKG-RPG\\Assets\\Resources\\scripts\\Configs\\SwitchCameras.cs", "RelativeToolTip": "Assets\\Resources\\scripts\\Configs\\SwitchCameras.cs", "ViewState": "AgIAAAMAAAAAAAAAAAAQwBgAAAAzAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-02-14T19:34:15.258Z"}, {"$type": "Document", "DocumentIndex": 19, "Title": "CharacterMods.cs", "DocumentMoniker": "D:\\Menino Autista\\DKG-RPG\\Assets\\Resources\\Scripts\\Characters\\CharacterMods.cs", "RelativeDocumentMoniker": "Assets\\Resources\\Scripts\\Characters\\CharacterMods.cs", "ToolTip": "D:\\Menino Autista\\DKG-RPG\\Assets\\Resources\\Scripts\\Characters\\CharacterMods.cs", "RelativeToolTip": "Assets\\Resources\\Scripts\\Characters\\CharacterMods.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAqwBUAAAAgAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-02-14T19:01:10.738Z"}, {"$type": "Document", "DocumentIndex": 18, "Title": "CharacterSkills.cs", "DocumentMoniker": "D:\\Menino Autista\\DKG-RPG\\Assets\\Resources\\scripts\\Characters\\CharacterSkills.cs", "RelativeDocumentMoniker": "Assets\\Resources\\scripts\\Characters\\CharacterSkills.cs", "ToolTip": "D:\\Menino Autista\\DKG-RPG\\Assets\\Resources\\scripts\\Characters\\CharacterSkills.cs", "RelativeToolTip": "Assets\\Resources\\scripts\\Characters\\CharacterSkills.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAABcAAABIAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-02-14T18:51:00.158Z"}, {"$type": "Document", "DocumentIndex": 17, "Title": "Types.cs", "DocumentMoniker": "D:\\Menino Autista\\DKG-RPG\\Assets\\Resources\\Scripts\\Types.cs", "RelativeDocumentMoniker": "Assets\\Resources\\Scripts\\Types.cs", "ToolTip": "D:\\Menino Autista\\DKG-RPG\\Assets\\Resources\\Scripts\\Types.cs", "RelativeToolTip": "Assets\\Resources\\Scripts\\Types.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAEAAAAqAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-02-18T13:47:57.13Z"}, {"$type": "Document", "DocumentIndex": 16, "Title": "DamageLabel.cs", "DocumentMoniker": "D:\\Menino Autista\\DKG-RPG\\Assets\\Resources\\Scripts\\UI\\DamageLabel.cs", "RelativeDocumentMoniker": "Assets\\Resources\\Scripts\\UI\\DamageLabel.cs", "ToolTip": "D:\\Menino Autista\\DKG-RPG\\Assets\\Resources\\Scripts\\UI\\DamageLabel.cs", "RelativeToolTip": "Assets\\Resources\\Scripts\\UI\\DamageLabel.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAABUAAAAgAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-02-17T14:56:05.444Z"}]}]}]}