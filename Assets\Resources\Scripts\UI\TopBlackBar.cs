using UnityEngine;

[RequireComponent(typeof(RectTransform))]
public class TopBlackBar : MonoBehaviour
{
    [SerializeField] private RectTransform _CanvasRect; // Assign your Canvas RectTransform in inspector

    private RectTransform rectTransform;
    private Rect lastSafeArea;

    void Awake()
    {
        rectTransform = GetComponent<RectTransform>();

        if (_CanvasRect == null)
        {
            Canvas canvas = GetComponentInParent<Canvas>();
            if (canvas != null)
                _CanvasRect = canvas.GetComponent<RectTransform>();
            else
                Debug.LogError("TopBlackBar: No canvas found in parent.");
        }

        ApplyTopBar();
    }

    void Update()
    {
        if (Screen.safeArea != lastSafeArea)
        {
            ApplyTopBar();
        }
    }

    private void ApplyTopBar()
    {
        if (_CanvasRect == null) return;

        lastSafeArea = Screen.safeArea;

        float canvasHeight = _CanvasRect.rect.height;
        float unsafeTopPixels = Screen.height - Screen.safeArea.yMax;
        float unsafeTopUnits = unsafeTopPixels * (canvasHeight / Screen.height);

        // Anchor the bar to the top of the canvas
        rectTransform.anchorMin = new Vector2(0, 1);
        rectTransform.anchorMax = new Vector2(1, 1);
        rectTransform.pivot = new Vector2(0.5f, 1);

        // Set the height of the black bar
        rectTransform.sizeDelta = new Vector2(0, unsafeTopUnits);

        // Keep the top edge pinned to the canvas
        rectTransform.anchoredPosition = Vector2.zero;
    }
}
