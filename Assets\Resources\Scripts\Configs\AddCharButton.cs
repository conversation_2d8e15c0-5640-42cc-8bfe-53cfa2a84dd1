using UnityEngine;
using UnityEngine.UI;

public class AddCharButton : MonoBehaviour
{
    ConfigsHandler configsHandler; // reference to the ConfigsHandler script

    void Start()
    {
        configsHandler = GameObject.Find("GameConfigsHandler").GetComponent<ConfigsHandler>(); // Get the ConfigsHandler script

        GetComponent<Button>().onClick.AddListener(onButtonPress); // Add a listener to the button that adds a character
    }

    void onButtonPress()
    {
        int index = configsHandler.AddCharacter(); // Add a character and get its index
         
        //new AddCharacter(index, configsHandler); // Create a new AddCharacter object
    }
}
