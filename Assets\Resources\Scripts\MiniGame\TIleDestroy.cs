using System.Collections;
using UnityEngine;

public class TileDestroy
{
    public GameObject circleEffect = new();
    private readonly GameObject starEffect = new();

    public bool isAnimating = false;

    public ParticleSystem.MainModule mainCircle;

    // creates the effects when a tile is destroyed, same thing that happen with the tiles
    // i didn't know how to use prefabs, if changed to prefabs, initialize it with the tiles transform
    // not as a child of the tile
    public TileDestroy(Vector3 pos, Color firstColor, Color secondColor)
    {
        // Sets their positions
        circleEffect.transform.position = pos;
        starEffect.transform.position = pos;

        // Circle Effects configs
        circleEffect.name = "circleEffect";
        var partical = circleEffect.AddComponent<ParticleSystem>();
        mainCircle = partical.main;
        var renderer = circleEffect.GetComponent<ParticleSystemRenderer>();
        var colorOverLifetime = partical.colorOverLifetime;
        var shape = partical.shape;
        mainCircle.loop = false;
        mainCircle.startLifetime = 0.1f;
        mainCircle.stopAction = ParticleSystemStopAction.Destroy;
        mainCircle.startSize = 2f;
        mainCircle.startSpeed = 0f;
        mainCircle.maxParticles = 1;

        shape.enabled = false;

        // Loads the circle material
        renderer.material = Resources.Load<Material>($@"Sprites/BattleEffects/clear_circle_material");

        // Makes it flash over time between two colors that can be set on the grid GameObject
        colorOverLifetime.enabled = true;

        var gran = new Gradient();
        gran.SetKeys(
            new GradientColorKey[] 
            {
                new(firstColor, 0.5f), 
                new(secondColor, 1f) 
            }, 
            new GradientAlphaKey[] 
            { 
                new(1.0f, 0.5f), 
                new(1.0f, 0.1f) 
            });
        colorOverLifetime.color = gran;

        // Star Effects configs
        starEffect.name = "starEffect";
        var particalStar = starEffect.AddComponent<ParticleSystem>();
        var mainStar = particalStar.main;
        var rendererStar = starEffect.GetComponent<ParticleSystemRenderer>();
        var emissionStar = particalStar.emission;
        var shapeStar = particalStar.shape;

        particalStar.Stop();
        mainStar.duration = 0.2f;
        particalStar.Play();

        mainStar.loop = false;
        mainStar.startLifetime = 0.2f;
        mainStar.startSpeed = 5;
        mainStar.startSize = 0.5f;
        mainStar.startColor = new Color(1, 1, 1, 0.5f);
        mainStar.gravityModifier = 3;
        mainStar.maxParticles = 2;
        mainStar.stopAction = ParticleSystemStopAction.Destroy;

        emissionStar.rateOverTime = 25;

        shapeStar.shapeType = ParticleSystemShapeType.Circle;
        shapeStar.arc = 180;
        shapeStar.scale = new Vector3(0.1f, 0.1f, 0.1f);

        // Loads the star material
        rendererStar.material = Resources.Load<Material>(@"Sprites/BattleEffects/clear_star_material");
    }

    public IEnumerator Effect() // Animates the circle effect by scaling it down in a constant speed
    {
        isAnimating = true;

        while (circleEffect.transform.localScale.x > 0.05f)
        {
            circleEffect.transform.localScale -= 4f * Time.deltaTime * Vector3.one;
            yield return null;
        }
        // stops the circle effect so it auto destroys
        circleEffect.GetComponent<ParticleSystem>().Stop();
    }
}
