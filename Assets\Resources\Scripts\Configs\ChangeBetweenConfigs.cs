using UnityEngine;
using UnityEngine.UI;

public class ChangeBetweenConfigs : MonoBehaviour
{
    public GameObject config1, config2;

    public Camera configCamera;

    // Start is called once before the first execution of Update after the MonoBehaviour is created
    void Start()
    {
        GetComponent<Button>().onClick.AddListener(delegate
        {
            if (IsPointerOverMenu())
            {
                config1.SetActive(!config1.activeSelf);
                config2.SetActive(!config2.activeSelf);
            }
        });   
    }

    bool IsPointerOverMenu() // Checks if the cursor is over the menu
    {
        Vector2 cursorPos = Input.GetTouch(0).position;


        return RectTransformUtility.RectangleContainsScreenPoint(GetComponent<RectTransform>(), cursorPos, configCamera);
    }
}
