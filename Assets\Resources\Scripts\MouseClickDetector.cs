using UnityEngine;
using UnityEngine.UI;
using UnityEngine.EventSystems;
using System.Collections.Generic;

/// <summary>
/// MouseClickDetector - A comprehensive Unity script that detects mouse clicks on GameObjects and UI elements.
/// This script can detect clicks on 3D objects, 2D objects, and Canvas-based UI elements, logging details to the Unity Console.
///
/// Features:
/// - Detects left mouse button clicks on 3D objects, 2D objects, and UI elements
/// - Uses Physics raycasting for 3D/2D objects and GraphicRaycaster for UI elements
/// - Logs clicked object/element names and details to Unity Console
/// - UI detection works with all Canvas-based components (Button, Image, Text, etc.)
/// - Handles overlapping elements correctly (UI renders on top)
/// - Includes comprehensive error handling and validation
/// - Runtime configuration and status monitoring
/// - Easy to attach to any GameObject (recommended: Camera or GameManager)
///
/// Usage:
/// 1. Attach this script to a GameObject in your scene (Camera or GameManager recommended)
/// 2. Configure the detection settings in the inspector (3D, 2D, UI toggles)
/// 3. Ensure your scene has:
///    - Colliders on 3D/2D objects you want to detect
///    - EventSystem in scene for UI detection
///    - GraphicRaycaster on Canvas components for UI detection
/// 4. Run the scene and click on objects/UI elements to see details logged in the Console
/// </summary>
public class MouseClickDetector : MonoBehaviour
{
    [Header("Detection Settings")]
    [Tooltip("Enable detection for 3D objects with Collider components")]
    public bool detect3DObjects = true;
    
    [Tooltip("Enable detection for 2D objects with Collider2D components")]
    public bool detect2DObjects = true;

    [Tooltip("Enable detection for UI elements (Canvas-based components)")]
    public bool detectUIElements = true;

    [Tooltip("Maximum distance for 3D raycasting (0 = infinite)")]
    public float maxRaycastDistance = 100f;

    [Tooltip("Layers to include in raycast detection (leave empty for all layers)")]
    public LayerMask raycastLayers = -1;
    
    [Header("Debug Settings")]
    [Tooltip("Enable verbose logging for debugging purposes")]
    public bool enableVerboseLogging = false;
    
    [Tooltip("Log when clicking on empty space")]
    public bool logEmptyClicks = true;

    // Camera reference for raycasting
    private Camera mainCamera;

    // UI raycasting components
    private EventSystem eventSystem;
    private List<GraphicRaycaster> graphicRaycasters = new List<GraphicRaycaster>();
    private PointerEventData pointerEventData;

    /// <summary>
    /// Initialize the script and validate setup
    /// </summary>
    void Start()
    {
        InitializeCamera();
        InitializeUIComponents();
        ValidateSetup();
    }

    /// <summary>
    /// Initialize camera reference
    /// </summary>
    private void InitializeCamera()
    {
        // Try to get camera from this GameObject first
        mainCamera = GetComponent<Camera>();
        
        // If no camera on this GameObject, use the main camera
        if (mainCamera == null)
        {
            mainCamera = Camera.main;
        }
        
        // If still no camera found, find any camera in the scene
        if (mainCamera == null)
        {
            mainCamera = FindFirstObjectByType<Camera>();
        }
        
        // Log camera status
        if (mainCamera != null)
        {
            if (enableVerboseLogging)
            {
                Debug.Log($"[MouseClickDetector] Using camera: {mainCamera.name}");
            }
        }
        else
        {
            Debug.LogError("[MouseClickDetector] No camera found! Mouse click detection will not work.");
        }
    }

    /// <summary>
    /// Initialize UI raycasting components
    /// </summary>
    private void InitializeUIComponents()
    {
        // Find or create EventSystem
        eventSystem = EventSystem.current;
        if (eventSystem == null)
        {
            eventSystem = FindFirstObjectByType<EventSystem>();
        }

        if (eventSystem == null && detectUIElements)
        {
            Debug.LogWarning("[MouseClickDetector] No EventSystem found in scene. UI detection may not work properly. " +
                           "Consider adding an EventSystem to your scene.");
        }

        // Find all GraphicRaycasters in the scene
        RefreshGraphicRaycasters();

        // Initialize pointer event data
        if (eventSystem != null)
        {
            pointerEventData = new PointerEventData(eventSystem);
        }

        if (enableVerboseLogging)
        {
            Debug.Log($"[MouseClickDetector] UI Components initialized. EventSystem: {(eventSystem != null ? "Found" : "Not found")}, " +
                     $"GraphicRaycasters: {graphicRaycasters.Count}");
        }
    }

    /// <summary>
    /// Refresh the list of GraphicRaycasters (useful when canvases are created/destroyed at runtime)
    /// </summary>
    private void RefreshGraphicRaycasters()
    {
        graphicRaycasters.Clear();
        GraphicRaycaster[] raycasters = FindObjectsByType<GraphicRaycaster>(FindObjectsSortMode.None);

        foreach (GraphicRaycaster raycaster in raycasters)
        {
            if (raycaster.enabled && raycaster.gameObject.activeInHierarchy)
            {
                graphicRaycasters.Add(raycaster);
            }
        }

        if (enableVerboseLogging && detectUIElements)
        {
            Debug.Log($"[MouseClickDetector] Found {graphicRaycasters.Count} active GraphicRaycasters");
        }
    }

    /// <summary>
    /// Validate the setup and warn about potential issues
    /// </summary>
    private void ValidateSetup()
    {
        if (!detect3DObjects && !detect2DObjects && !detectUIElements)
        {
            Debug.LogWarning("[MouseClickDetector] All detection types (3D, 2D, and UI) are disabled. No objects will be detected.");
        }

        if (maxRaycastDistance <= 0)
        {
            maxRaycastDistance = Mathf.Infinity;
            if (enableVerboseLogging)
            {
                Debug.Log("[MouseClickDetector] Raycast distance set to infinite.");
            }
        }
    }

    /// <summary>
    /// Update method - checks for mouse input each frame
    /// </summary>
    void Update()
    {
        // Check for left mouse button click
        if (Input.GetMouseButtonDown(0))
        {
            HandleMouseClick();
        }
    }

    /// <summary>
    /// Handle mouse click detection and processing
    /// </summary>
    private void HandleMouseClick()
    {
        if (mainCamera == null && (detect3DObjects || detect2DObjects))
        {
            Debug.LogError("[MouseClickDetector] Cannot detect 3D/2D clicks - no camera available!");
            return;
        }

        Vector3 mousePosition = Input.mousePosition;
        bool objectDetected = false;

        if (enableVerboseLogging)
        {
            Debug.Log($"[MouseClickDetector] Mouse clicked at screen position: {mousePosition}");
        }

        // Try UI detection first (UI typically renders on top)
        if (detectUIElements)
        {
            objectDetected = TryDetectUIElement(mousePosition);
        }

        // Try 3D detection if UI didn't find anything
        if (!objectDetected && detect3DObjects)
        {
            objectDetected = TryDetect3DObject(mousePosition);
        }

        // Try 2D detection if 3D didn't find anything
        if (!objectDetected && detect2DObjects)
        {
            objectDetected = TryDetect2DObject(mousePosition);
        }

        // Log empty click if nothing was detected
        if (!objectDetected && logEmptyClicks)
        {
            Debug.Log("[MouseClickDetector] Clicked on empty space - no object detected.");
        }
    }

    /// <summary>
    /// Attempt to detect 3D objects using raycasting
    /// </summary>
    /// <param name="mousePosition">Screen position of the mouse</param>
    /// <returns>True if an object was detected</returns>
    private bool TryDetect3DObject(Vector3 mousePosition)
    {
        try
        {
            // Create ray from camera through mouse position
            Ray ray = mainCamera.ScreenPointToRay(mousePosition);
            RaycastHit hit;

            // Perform raycast
            if (Physics.Raycast(ray, out hit, maxRaycastDistance, raycastLayers))
            {
                GameObject clickedObject = hit.collider.gameObject;
                
                // Validate the hit object
                if (clickedObject != null)
                {
                    LogClickedObject(clickedObject, "3D", hit.point);
                    return true;
                }
                else
                {
                    Debug.LogWarning("[MouseClickDetector] 3D Raycast hit but GameObject is null!");
                }
            }
        }
        catch (System.Exception e)
        {
            Debug.LogError($"[MouseClickDetector] Error during 3D raycast: {e.Message}");
        }

        return false;
    }

    /// <summary>
    /// Attempt to detect 2D objects using raycasting
    /// </summary>
    /// <param name="mousePosition">Screen position of the mouse</param>
    /// <returns>True if an object was detected</returns>
    private bool TryDetect2DObject(Vector3 mousePosition)
    {
        try
        {
            // Convert mouse position to world position for 2D
            Vector3 worldPosition = mainCamera.ScreenToWorldPoint(mousePosition);
            worldPosition.z = 0f; // Ensure Z is 0 for 2D

            // Perform 2D raycast
            RaycastHit2D hit = Physics2D.Raycast(worldPosition, Vector2.zero, 0f, raycastLayers);

            if (hit.collider != null)
            {
                GameObject clickedObject = hit.collider.gameObject;
                
                // Validate the hit object
                if (clickedObject != null)
                {
                    LogClickedObject(clickedObject, "2D", hit.point);
                    return true;
                }
                else
                {
                    Debug.LogWarning("[MouseClickDetector] 2D Raycast hit but GameObject is null!");
                }
            }
        }
        catch (System.Exception e)
        {
            Debug.LogError($"[MouseClickDetector] Error during 2D raycast: {e.Message}");
        }

        return false;
    }

    /// <summary>
    /// Attempt to detect UI elements using GraphicRaycaster
    /// </summary>
    /// <param name="mousePosition">Screen position of the mouse</param>
    /// <returns>True if a UI element was detected</returns>
    private bool TryDetectUIElement(Vector3 mousePosition)
    {
        if (eventSystem == null || pointerEventData == null)
        {
            if (enableVerboseLogging)
            {
                Debug.LogWarning("[MouseClickDetector] UI detection skipped - EventSystem or PointerEventData not available");
            }
            return false;
        }

        try
        {
            // Set up pointer event data
            pointerEventData.position = mousePosition;

            // Check each GraphicRaycaster for UI hits
            foreach (GraphicRaycaster raycaster in graphicRaycasters)
            {
                if (raycaster == null || !raycaster.enabled || !raycaster.gameObject.activeInHierarchy)
                    continue;

                List<RaycastResult> results = new List<RaycastResult>();
                raycaster.Raycast(pointerEventData, results);

                // Process the first valid result
                foreach (RaycastResult result in results)
                {
                    if (result.gameObject != null)
                    {
                        LogClickedUIElement(result.gameObject, result);
                        return true;
                    }
                }
            }
        }
        catch (System.Exception e)
        {
            Debug.LogError($"[MouseClickDetector] Error during UI raycast: {e.Message}");
        }

        return false;
    }

    /// <summary>
    /// Log information about the clicked UI element
    /// </summary>
    /// <param name="clickedUIElement">The UI GameObject that was clicked</param>
    /// <param name="raycastResult">The raycast result containing additional information</param>
    private void LogClickedUIElement(GameObject clickedUIElement, RaycastResult raycastResult)
    {
        string elementName = string.IsNullOrEmpty(clickedUIElement.name) ? "[Unnamed UI Element]" : clickedUIElement.name;

        // Get UI component type for better identification
        string componentInfo = GetUIComponentInfo(clickedUIElement);

        // Basic log message
        Debug.Log($"[MouseClickDetector] Clicked on UI element: '{elementName}' {componentInfo}");

        // Verbose logging with additional information
        if (enableVerboseLogging)
        {
            string canvasInfo = GetCanvasInfo(clickedUIElement);
            string hierarchyPath = GetUIHierarchyPath(clickedUIElement);

            Debug.Log($"[MouseClickDetector] UI Detailed info - Element: '{elementName}' {componentInfo}, " +
                     $"Canvas: {canvasInfo}, Hierarchy: {hierarchyPath}, " +
                     $"Screen Position: {raycastResult.screenPosition}, " +
                     $"Distance: {raycastResult.distance:F2}, " +
                     $"Sort Order: {raycastResult.sortingOrder}");
        }
    }

    /// <summary>
    /// Get information about UI components on the GameObject
    /// </summary>
    /// <param name="uiObject">The UI GameObject to analyze</param>
    /// <returns>String describing the UI components</returns>
    private string GetUIComponentInfo(GameObject uiObject)
    {
        List<string> components = new List<string>();

        // Check for common UI components
        if (uiObject.GetComponent<Button>() != null) components.Add("Button");
        if (uiObject.GetComponent<Image>() != null) components.Add("Image");
        if (uiObject.GetComponent<Text>() != null) components.Add("Text");
        if (uiObject.GetComponent<TMPro.TextMeshProUGUI>() != null) components.Add("TextMeshPro");
        if (uiObject.GetComponent<InputField>() != null) components.Add("InputField");
        if (uiObject.GetComponent<TMPro.TMP_InputField>() != null) components.Add("TMP_InputField");
        if (uiObject.GetComponent<Toggle>() != null) components.Add("Toggle");
        if (uiObject.GetComponent<Slider>() != null) components.Add("Slider");
        if (uiObject.GetComponent<Scrollbar>() != null) components.Add("Scrollbar");
        if (uiObject.GetComponent<Dropdown>() != null) components.Add("Dropdown");
        if (uiObject.GetComponent<TMPro.TMP_Dropdown>() != null) components.Add("TMP_Dropdown");
        if (uiObject.GetComponent<ScrollRect>() != null) components.Add("ScrollRect");

        // Check for custom components that inherit from Graphic
        Graphic graphic = uiObject.GetComponent<Graphic>();
        if (graphic != null && components.Count == 0)
        {
            components.Add($"Graphic({graphic.GetType().Name})");
        }

        return components.Count > 0 ? $"({string.Join(", ", components)})" : "(UI Element)";
    }

    /// <summary>
    /// Get information about the Canvas containing the UI element
    /// </summary>
    /// <param name="uiObject">The UI GameObject to analyze</param>
    /// <returns>String describing the Canvas</returns>
    private string GetCanvasInfo(GameObject uiObject)
    {
        Canvas canvas = uiObject.GetComponentInParent<Canvas>();
        if (canvas != null)
        {
            string canvasName = string.IsNullOrEmpty(canvas.name) ? "[Unnamed Canvas]" : canvas.name;
            return $"{canvasName} ({canvas.renderMode})";
        }
        return "No Canvas";
    }

    /// <summary>
    /// Get the hierarchy path of the UI element for better identification
    /// </summary>
    /// <param name="uiObject">The UI GameObject to analyze</param>
    /// <returns>String representing the hierarchy path</returns>
    private string GetUIHierarchyPath(GameObject uiObject)
    {
        List<string> path = new List<string>();
        Transform current = uiObject.transform;

        // Build path up to Canvas or root
        while (current != null)
        {
            string name = string.IsNullOrEmpty(current.name) ? "[Unnamed]" : current.name;
            path.Insert(0, name);

            // Stop at Canvas level
            if (current.GetComponent<Canvas>() != null)
                break;

            current = current.parent;
        }

        return string.Join("/", path);
    }

    /// <summary>
    /// Log information about the clicked object
    /// </summary>
    /// <param name="clickedObject">The GameObject that was clicked</param>
    /// <param name="detectionType">Type of detection used (2D or 3D)</param>
    /// <param name="hitPoint">World position where the click occurred</param>
    private void LogClickedObject(GameObject clickedObject, string detectionType, Vector3 hitPoint)
    {
        string objectName = clickedObject.name;
        
        // Basic log message
        Debug.Log($"[MouseClickDetector] Clicked on {detectionType} object: '{objectName}'");
        
        // Verbose logging with additional information
        if (enableVerboseLogging)
        {
            string parentInfo = clickedObject.transform.parent != null 
                ? $" (Parent: {clickedObject.transform.parent.name})" 
                : " (No parent)";
            
            Debug.Log($"[MouseClickDetector] Detailed info - Object: '{objectName}'{parentInfo}, " +
                     $"Position: {clickedObject.transform.position}, Hit Point: {hitPoint}, " +
                     $"Layer: {LayerMask.LayerToName(clickedObject.layer)} ({clickedObject.layer})");
        }
    }

    /// <summary>
    /// Public method to manually trigger click detection at a specific screen position
    /// Useful for testing or custom input handling
    /// </summary>
    /// <param name="screenPosition">Screen position to check for objects</param>
    /// <returns>The GameObject that was detected, or null if none</returns>
    public GameObject DetectObjectAtPosition(Vector3 screenPosition)
    {
        // Try UI detection first
        if (detectUIElements && eventSystem != null && pointerEventData != null)
        {
            pointerEventData.position = screenPosition;

            foreach (GraphicRaycaster raycaster in graphicRaycasters)
            {
                if (raycaster == null || !raycaster.enabled || !raycaster.gameObject.activeInHierarchy)
                    continue;

                List<RaycastResult> results = new List<RaycastResult>();
                raycaster.Raycast(pointerEventData, results);

                if (results.Count > 0 && results[0].gameObject != null)
                {
                    return results[0].gameObject;
                }
            }
        }

        if (mainCamera == null)
        {
            Debug.LogError("[MouseClickDetector] Cannot detect 3D/2D objects - no camera available!");
            return null;
        }

        // Try 3D detection
        if (detect3DObjects)
        {
            Ray ray = mainCamera.ScreenPointToRay(screenPosition);
            RaycastHit hit;

            if (Physics.Raycast(ray, out hit, maxRaycastDistance, raycastLayers))
            {
                return hit.collider.gameObject;
            }
        }

        // Try 2D detection
        if (detect2DObjects)
        {
            Vector3 worldPosition = mainCamera.ScreenToWorldPoint(screenPosition);
            worldPosition.z = 0f;

            RaycastHit2D hit = Physics2D.Raycast(worldPosition, Vector2.zero, 0f, raycastLayers);

            if (hit.collider != null)
            {
                return hit.collider.gameObject;
            }
        }

        return null;
    }

    /// <summary>
    /// Enable or disable the click detection at runtime
    /// </summary>
    /// <param name="enabled">Whether to enable click detection</param>
    public void SetDetectionEnabled(bool enabled)
    {
        this.enabled = enabled;

        if (enableVerboseLogging)
        {
            Debug.Log($"[MouseClickDetector] Detection {(enabled ? "enabled" : "disabled")}");
        }
    }

    /// <summary>
    /// Enable or disable specific detection types at runtime
    /// </summary>
    /// <param name="enable3D">Enable 3D object detection</param>
    /// <param name="enable2D">Enable 2D object detection</param>
    /// <param name="enableUI">Enable UI element detection</param>
    public void SetDetectionTypes(bool enable3D, bool enable2D, bool enableUI)
    {
        detect3DObjects = enable3D;
        detect2DObjects = enable2D;
        detectUIElements = enableUI;

        if (enableVerboseLogging)
        {
            Debug.Log($"[MouseClickDetector] Detection types updated - 3D: {enable3D}, 2D: {enable2D}, UI: {enableUI}");
        }
    }

    /// <summary>
    /// Refresh UI components (useful when canvases are created/destroyed at runtime)
    /// </summary>
    public void RefreshUIComponents()
    {
        RefreshGraphicRaycasters();

        if (enableVerboseLogging)
        {
            Debug.Log($"[MouseClickDetector] UI components refreshed. Found {graphicRaycasters.Count} GraphicRaycasters");
        }
    }

    /// <summary>
    /// Get the current detection status
    /// </summary>
    /// <returns>String describing current detection settings</returns>
    public string GetDetectionStatus()
    {
        return $"MouseClickDetector Status - 3D: {detect3DObjects}, 2D: {detect2DObjects}, UI: {detectUIElements}, " +
               $"Camera: {(mainCamera != null ? mainCamera.name : "None")}, " +
               $"EventSystem: {(eventSystem != null ? "Available" : "None")}, " +
               $"UI Raycasters: {graphicRaycasters.Count}";
    }
}
