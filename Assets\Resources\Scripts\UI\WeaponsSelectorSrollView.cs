using System.Collections;
using System.Collections.Generic;
using System.Linq;
using TMPro;
using UnityEngine;
using UnityEngine.UI;

//TODO: Implement virtualization and pooling
public class WeaponsSelectorSrollView : MonoBehaviour
{
    public ConfigsHandler configsHandler;

    //Pooling and Virtualization
    [SerializeField] private ScrollRect scrollRect;
    private float itemWidth = 330f; // width of one weapon prefab
    private int buffer = 2; // extra items off-screen to avoid popping
    private float itemSpacing = 20f;

    private int visibleItemCount;
    private int totalItemCount;
    private List<Weapons> weapons = new();
    //private List<Weapons> filteredWeapons = new();
    //private List<Weapons> noBlueprints = new();
    private Queue<GameObject> pooledItems = new(); // Object pool
    private Dictionary<int, GameObject> activeItems = new(); // index -> GameObject


    public BattleCharacter selectedCharacter;
    public BattleCharacter previousCharacter;
    Vector3 origin; // The origin of the UI
    public Button closeButton;

    public Transform content;
    public GameObject weaponPrefab;

    [SerializeField] private Material grayscaleMaterial; // Assign this in Inspector

    // Filters
    public Button allFilterButton, atkFilterButton, lvlFilterButton, rarityFilterButton, slotsFilterButton, durabilityFilterButton, ascOrderFilterButton, dscOrderFilterButton;
    TextMeshProUGUI allFilterText, atkFilterText, lvlFilterText, rarityFilterText, slotsFilterText, durabilityFilterText;
    private string currentFilter = "None";      // "ATKw", "Rarity", "Level"
    private bool sortDescending = true;


    public Weapons selectedWeapon;

    // Start is called once before the first execution of Update after the MonoBehaviour is created
    void Start()
    {
        configsHandler = GameObject.Find("GameConfigsHandler").GetComponent<ConfigsHandler>(); // Get the ConfigsHandler script

        gameObject.SetActive(false);
        origin = new(0f, -3f * transform.localScale.y, transform.position.z); // Set the origin
        closeButton.onClick.AddListener(() =>
        {
            gameObject.SetActive(false);
        });

        LoadWeapons();

        //Filter Section
        allFilterButton.onClick.AddListener(() => FilterByATK());
        atkFilterButton.onClick.AddListener(() => FilterByLevel());
        lvlFilterButton.onClick.AddListener(() => FilterByRarity());
        rarityFilterButton.onClick.AddListener(() => FilterByDurability());
        durabilityFilterButton.onClick.AddListener(() => DefaultFilter());
        ascOrderFilterButton.onClick.AddListener(() => OrderByDsc());
        dscOrderFilterButton.onClick.AddListener(() => OrderByAsc());

        allFilterText = allFilterButton.transform.GetChild(0).GetComponent<TextMeshProUGUI>();
        atkFilterText = atkFilterButton.transform.GetChild(0).GetComponent<TextMeshProUGUI>();
        lvlFilterText = lvlFilterButton.transform.GetChild(0).GetComponent<TextMeshProUGUI>();
        rarityFilterText = rarityFilterButton.transform.GetChild(0).GetComponent<TextMeshProUGUI>();
        slotsFilterText = slotsFilterButton.transform.GetChild(0).GetComponent<TextMeshProUGUI>();
        durabilityFilterText = durabilityFilterButton.transform.GetChild(0).GetComponent<TextMeshProUGUI>();

        allFilterText.text = KeywordManager.GetWord("MENU_SORT_POWER-UP_All");
        atkFilterText.text = KeywordManager.GetWord("MENU_SORT_WEAPON_ATTACK");
        lvlFilterText.text = KeywordManager.GetWord("MENU_SORT_WEAPON_LEVEL");
        rarityFilterText.text = KeywordManager.GetWord("MENU_SORT_WEAPON_RARITY");
        slotsFilterText.text = KeywordManager.GetWord("MENU_SORT_WEAPON_SLOTS");
        durabilityFilterText.text = KeywordManager.GetWord("MENU_SORT_WEAPON_DURABILITY");
    }

    // Update is called once per frame
    void Update()
    {
        selectedCharacter = configsHandler.GetSelectedPlayerCharacter();

        if (selectedCharacter != previousCharacter)
        {
            previousCharacter = selectedCharacter;
            selectedWeapon = null;
            RefreshScrollView(); // Reload filtered list

        }

        UpdateVisibleItems();

        // If the weapon is being used, turn on grayscale
        foreach (var weapon in activeItems.Values)
        {

            var graphics = weapon.GetComponentsInChildren<Graphic>(includeInactive: true);
            foreach (var graphic in graphics)
            {
                if (graphic.gameObject.name == "selectedOverlay") continue;
                if (graphic.gameObject.name == "equipButton") continue;
                if (graphic.gameObject.name == "removeButton") continue;
                if (graphic.gameObject.name == "detailsButton") continue;

                graphic.material = weapon.GetComponent<WeaponPrefab>().weapon.isBeingUsed ? grayscaleMaterial : null;
            }

        }
    }



    void OnEnable()
    {
        transform.position = origin; // Set the position
    }

    void LoadWeapons()
    {
        //GetWeaponsAfterRequirements(); // populate filteredWeapons
        weapons = GetWeaponsAfterRequirements();     // use filtered list
        totalItemCount = weapons.Count;

        // Set content width
        //Account for spacing between items
        float contentWidth = (itemWidth + itemSpacing) * totalItemCount + itemSpacing;
        RectTransform contentRect = content.GetComponent<RectTransform>();
        contentRect.sizeDelta = new Vector2(contentWidth, contentRect.sizeDelta.y);

        // Determine how many items fit in view
        float viewportWidth = scrollRect.viewport.rect.width;
        visibleItemCount = Mathf.CeilToInt(viewportWidth / itemWidth) + buffer;

        // Pre-instantiate pooled items, up to the number of visible items
        int poolTargetSize = Mathf.Min(visibleItemCount, weapons.Count);
        while (pooledItems.Count < poolTargetSize)
        {
            var go = Instantiate(weaponPrefab, content);
            go.SetActive(false);
            pooledItems.Enqueue(go);
        }

        UpdateVisibleItems(); // Initial update

    }

    void UpdateVisibleItems()
    {
        float contentX = -content.GetComponent<RectTransform>().anchoredPosition.x; // how far we've scrolled (positive value)
        float viewportWidth = scrollRect.viewport.rect.width;

        float itemTotalWidth = itemWidth + itemSpacing;
        float visibilityBuffer = 50f;

        // Calculate the visible range with a buffer to keep partially visible items
        float startX = contentX - visibilityBuffer;
        float endX = contentX + viewportWidth + visibilityBuffer;

        int firstVisibleIndex = Mathf.FloorToInt(startX / itemTotalWidth);
        int lastVisibleIndex = Mathf.CeilToInt(endX / itemTotalWidth);

        firstVisibleIndex = Mathf.Max(0, firstVisibleIndex);
        lastVisibleIndex = Mathf.Min(totalItemCount - 1, lastVisibleIndex);

        // Track which indices are supposed to be visible
        var indicesToKeep = new HashSet<int>();
        for (int i = firstVisibleIndex; i <= lastVisibleIndex; i++)
        {
            indicesToKeep.Add(i);

            if (!activeItems.ContainsKey(i))
            {
                GameObject item = GetPooledItem();
                SetupItem(item, i);
                activeItems[i] = item;
            }
        }

        // Recycle items no longer in view
        List<int> toRemove = new List<int>();
        foreach (var kvp in activeItems)
        {
            if (!indicesToKeep.Contains(kvp.Key))
            {
                kvp.Value.SetActive(false);
                kvp.Value.GetComponent<WeaponPrefab>().CleanupForPooling();
                pooledItems.Enqueue(kvp.Value);
                toRemove.Add(kvp.Key);
            }
        }

        foreach (int index in toRemove)
            activeItems.Remove(index);
    }

    GameObject GetPooledItem()
    {
        if (pooledItems.Count > 0)
            return pooledItems.Dequeue();

        // Only create more if really necessary (scrolling fast or small pool)
        var go = Instantiate(weaponPrefab, content);
        go.SetActive(false);
        return go;
    }

    void SetupItem(GameObject item, int index)
    {
        item.SetActive(true);
        var weapon = weapons[index];
        var rect = item.GetComponent<RectTransform>();
        //Add left padding of spacing
        rect.anchoredPosition = new Vector2(index * (itemWidth + itemSpacing) + itemSpacing, 0f);

        var wp = item.GetComponent<WeaponPrefab>();
        wp.weapon = weapon;
        wp.RefreshUI(); // Updates the UI from the weapon data
    }

    void RefreshScrollView()
    {
        // Clear current items
        foreach (var item in activeItems.Values)
        {
            item.SetActive(false);
            item.GetComponent<WeaponPrefab>().CleanupForPooling();
            pooledItems.Enqueue(item);
        }
        activeItems.Clear();

        LoadWeapons(); // Load filtered weapons and update scroll view

        RefreshAllUI();

        // Reset scroll position
        //scrollRect.horizontalNormalizedPosition = 0f;
    }

    // Filters weapons based on selected character knowledge and luck
    public List<Weapons> GetWeaponsAfterRequirements()
    {
        // If selected character is null, return
        if (selectedCharacter == null) return new();

        List<Weapons> filteredWeapons = new();

        selectedCharacter = configsHandler.GetSelectedPlayerCharacter();

        if (selectedCharacter == null || selectedCharacter.mods == null) return new();

        //FilterOutBlueprints();

        filteredWeapons = ApplyFilters().FindAll(w =>
            selectedCharacter.mods.GetKnowledge() >= w.qiMin &&
            selectedCharacter.mods.GetLuck() >= w.luckMin
        );

        return filteredWeapons;
    }

    public List<Weapons> FilterOutBlueprints()
    {
        List<Weapons> noBlueprints = new();

        foreach (var weapon in configsHandler.weapons)
        {
            if (!weapon.name.Contains("Blueprint"))
            {
                noBlueprints.Add(weapon);
            }
        }
        return noBlueprints;
    }

    public void RefreshAllUI()
    {
        WeaponPrefab[] allWeapons = FindObjectsByType<WeaponPrefab>(FindObjectsSortMode.None);
        foreach (var weapon in allWeapons)
        {
            weapon.RefreshUI();
        }
    }

    #region Filters

    public List<Weapons> ApplyFilters()
    {
        IEnumerable<Weapons> filtered;
        List<Weapons> noBlueprints = new();
        noBlueprints = FilterOutBlueprints();


        switch (currentFilter)
        {
            case "ATK":
                filtered = sortDescending
                    ? noBlueprints.OrderByDescending(w => w.atkw)
                    : noBlueprints.OrderBy(w => w.atkw);
                break;

            case "Level":
                filtered = sortDescending
                    ? noBlueprints.OrderByDescending(w => w.wlBase)
                    : noBlueprints.OrderBy(w => w.wlBase);
                break;

            case "Rarity":
                filtered = sortDescending
                    ? noBlueprints.OrderByDescending(w => w.numberOfStars)
                    : noBlueprints.OrderBy(w => w.numberOfStars);
                break;

            case "Durability":
                filtered = sortDescending
                    ? noBlueprints.OrderByDescending(w => w.shots)
                    : noBlueprints.OrderBy(w => w.shots);
                break;

            case "None":
            default:
                filtered = sortDescending
                    ? noBlueprints // Preserve original order
                    : noBlueprints.AsEnumerable().Reverse();
                break;
        }

        List<Weapons> filteredWeapons = filtered.ToList();

        return filteredWeapons;
    }

    public void FilterByATK()
    {
        currentFilter = "ATK";
        allFilterButton.gameObject.SetActive(false);
        atkFilterButton.gameObject.SetActive(true);
        RefreshScrollView();
    }

    public void FilterByLevel()
    {
        currentFilter = "Level";
        atkFilterButton.gameObject.SetActive(false);
        lvlFilterButton.gameObject.SetActive(true);
        RefreshScrollView();
    }

    public void FilterByRarity()
    {
        currentFilter = "Rarity";
        lvlFilterButton.gameObject.SetActive(false);
        rarityFilterButton.gameObject.SetActive(true);
        RefreshScrollView();
    }

    public void FilterByDurability()
    {
        currentFilter = "Durability";
        rarityFilterButton.gameObject.SetActive(false);
        durabilityFilterButton.gameObject.SetActive(true);
        RefreshScrollView();
    }

    public void DefaultFilter()
    {
        currentFilter = "None";
        durabilityFilterButton.gameObject.SetActive(false);
        allFilterButton.gameObject.SetActive(true);
        RefreshScrollView();

    }

    public void OrderByAsc()
    {
        sortDescending = false;
        dscOrderFilterButton.gameObject.SetActive(false);
        ascOrderFilterButton.gameObject.SetActive(true);
        RefreshScrollView();
    }

    public void OrderByDsc()
    {
        sortDescending = true;
        ascOrderFilterButton.gameObject.SetActive(false);
        dscOrderFilterButton.gameObject.SetActive(true);
        RefreshScrollView();
    }

    #endregion

}
