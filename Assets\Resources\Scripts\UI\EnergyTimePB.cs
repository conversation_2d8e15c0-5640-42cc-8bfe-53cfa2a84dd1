using DG.Tweening;
using UnityEngine;
using UnityEngine.UI;

public class EnergyTimePB : MonoBehaviour
{
    public GameObject ConfigHandler; // Reference to the ConfigsHandler GameObject
    public GameObject ppDisplay;
    private ConfigsHandler configsHandler; // Reference to the ConfigsHandler

    private Image progressImage; // Reference to the Image component
    private Image progressImageDelay;

    // Animation control variables
    private readonly float animationDuration = 0.5f;
    private Tween delayTween;

    void Start()
    {
        configsHandler = ConfigHandler.GetComponent<ConfigsHandler>(); // Get the ConfigsHandler script

        progressImage = transform.GetChild(1).GetComponent<Image>(); // Get the Image component

        progressImageDelay = transform.GetChild(0).GetComponent<Image>(); // Get the Image component
        progressImageDelay.color = Color.white;

    }

    void Update()
    {
        // changes the color between does two colors, yellow is for count down and the other is for PP amount
        if (configsHandler.canCountdown)
        {
            progressImage.color = Color.yellow;
            progressImageDelay.gameObject.SetActive(false);
        }
        else
        {
            progressImage.color = Tools.FromArgb(145, 183, 224);
            progressImageDelay.gameObject.SetActive(true);

            // Update delayed progress bar with smooth animation
            UpdateDelayedProgressBar(configsHandler.ppLeft / configsHandler.MaxPP);
        }

        // updates the fill amount of the Image component
        progressImage.fillAmount = configsHandler.canCountdown ? configsHandler.energyLeft / configsHandler.energyTimer : configsHandler.ppLeft / configsHandler.MaxPP;

        CanPPDisplayBeActive();
    }

    private void UpdateDelayedProgressBar(float targetFillAmount)
    {
        // Kill existing tween to prevent overlapping
        delayTween?.Kill();

        // Start smooth animation with ease-in
        delayTween = progressImageDelay.DOFillAmount(targetFillAmount, animationDuration)
            .SetEase(Ease.OutQuad);
    }
    
        void CanPPDisplayBeActive() // Checks if the PP display can be active
    {
        if (configsHandler.canCountdown) ppDisplay.SetActive(false);
        else ppDisplay.SetActive(true);
    }

}
