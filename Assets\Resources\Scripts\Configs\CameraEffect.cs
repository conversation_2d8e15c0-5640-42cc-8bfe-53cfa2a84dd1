using UnityEngine;
using DG.Tweening;

public class CameraEffect : MonoBehaviour
{
    public Camera worldCamera; // The perspective camera
    public Vector3 moveOffset = new Vector3(0, 1f, -1f); // How far the camera should move
    public float duration = 0.5f;

    private Vector3 originalPosition;
    private Quaternion originalRotation;

    public GameObject background;
    private bool isMoved = false;

    void Start()
    {
        originalPosition = worldCamera.transform.position;
        originalRotation = worldCamera.transform.rotation;
    }

    public void MoveToMenuView(Vector3 focusPoint, int index, GameObject actionMenu)
    {
        if (isMoved) return;

        isMoved = true;

        // Step 1: Direction from focus to original camera position
        Vector3 direction = originalPosition - focusPoint;

        // Step 2: Define rotation angles
        float horizontalAngle;
        float heightOffset;

        switch (index)
        {
            case 0:
                horizontalAngle = 25f;
                heightOffset = -1f;
                break;
            case 1:
                horizontalAngle = 15f;
                heightOffset = -1f;
                break;
            case 2:
                horizontalAngle = -15f;
                heightOffset = -1f;
                break;
            case 3:
                horizontalAngle = -25f;
                heightOffset = -1f;
                break;
            default:
                horizontalAngle = 0f;
                heightOffset = 0f;
                break;
        }

        // Step 3: Apply horizontal rotation
        Quaternion yawRotation = Quaternion.Euler(0, horizontalAngle, 0);
        Vector3 rotatedDirection = yawRotation * direction;

        // Step 4: Apply zoom factor
        float zoomFactor = 0.85f;
        Vector3 zoomedDirection = rotatedDirection * zoomFactor;

        // Step 5: Compute target position around focus point (without height offset)
        Vector3 targetPosition = focusPoint + zoomedDirection;

        // Step 6: Look at the focal point
        Quaternion targetRotation = Quaternion.LookRotation(focusPoint - targetPosition);

        // Step 7: Animate circular movement and rotation first
        worldCamera.transform.DOMove(targetPosition, duration).SetEase(Ease.OutCubic);
        worldCamera.transform.DORotateQuaternion(targetRotation, duration).SetEase(Ease.OutCubic);

        // Step 8: THEN apply height offset as separate vertical movement
        if (Mathf.Abs(heightOffset) > 0.01f)
        {
            Vector3 heightAdjustedPosition = targetPosition + new Vector3(0, heightOffset, 0);
            worldCamera.transform.DOMove(heightAdjustedPosition, duration)
                .SetEase(Ease.OutCubic)
                .SetDelay(duration * 0.3f); // Start height adjustment after 30% of main animation
        }

        // Step 9: Animate the action menu
        actionMenu.transform.DORotateQuaternion(targetRotation, duration).SetEase(Ease.OutCubic);

        // Step 10: Zoom the background
        background.transform.DOScale(1.2f, duration).SetEase(Ease.OutCubic);
    }

    public void ResetCamera()
    {
        if (!isMoved) return;

        isMoved = false;

        worldCamera.transform.DOMove(originalPosition, duration).SetEase(Ease.InCubic);
        worldCamera.transform.DORotateQuaternion(originalRotation, duration).SetEase(Ease.InCubic);
        background.transform.DOScale(1f, duration).SetEase(Ease.InCubic);
    }
}

