using UnityEngine;

public class StunEffect : MonoBehaviour
{
    public float speed = 10, spacingx = 0.3f, spacingy = 0.1f;
    public bool delayed = false;

    void Update()
    {
        float phaseShift = delayed ? Mathf.PI : 0;
        float offsetx = Mathf.Sin(Time.time * speed + phaseShift) * spacingx;
        float offsety = Mathf.Cos(Time.time * speed + phaseShift) * spacingy;

        transform.position = transform.parent.position + new Vector3(offsetx, offsety, 0);
    }
}
