using System.Collections;
using UnityEngine;

public class DamageStar : MonoBehaviour
{
    public float finalSpeedFactor = 0.1f; 

    public float rotationSpeed = 115f;

    public float multiplyer = 6f;

    public float time = 0.3f;


    public IEnumerator MoveObject(Vector3 to) // moves the object to a specific position over time
    {
        Vector3 from = transform.position;

        float distance = Vector3.Distance(from, to); // gets the distance between the two positions
        float initialSpeed = distance * multiplyer; // calculates the initial speed
        float finalSpeed = initialSpeed * finalSpeedFactor; // calculates the final speed
        float elapsedTime = 0f;

        while (elapsedTime < time) // loops until it had passed the time
        {
            elapsedTime += Time.deltaTime;
            float timeFractionPassed = elapsedTime / time; // calculates the fraction of time passed
            float currentSpeed = Mathf.Lerp(initialSpeed, finalSpeed, timeFractionPassed); // calculates the current speed

            transform.position = Vector3.MoveTowards(transform.position, to, currentSpeed * Time.deltaTime); // moves the object towards the target at the current speed
            transform.rotation *= Quaternion.Euler(0, 0, rotationSpeed * (Time.deltaTime * 60f)); // rotates the object

            yield return null;
        }

        // plays the damage sound
        ConfMenu.PlaySoundWithVolume(GameObject.Find("GameConfigsHandler").GetComponent<AudioSource>(), Resources.Load<AudioClip>("SFX/Damage"));


        // sets the position of the object to the target position
        transform.position = to;

        Destroy(gameObject);
    }
}