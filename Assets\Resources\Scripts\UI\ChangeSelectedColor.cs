using TMPro;
using UnityEngine;
using UnityEngine.UI;

public class ChangeSelectedColor : MonoBehaviour
{
    public Color color = Color.green;

    GameObject parentObject;

    void Start()
    {
        parentObject = transform.parent.gameObject;
    }

    void Update() // changes the color of the Object to show that is selected by using a Child object
    {
        if (parentObject.transform.GetChild(1).gameObject.activeSelf)
        {
            GetComponent<Image>().color = color;
            transform.GetChild(0).GetComponent<TextMeshProUGUI>().color = color;
        }
        else
        {
            GetComponent<Image>().color = Color.white;
            transform.GetChild(0).GetComponent<TextMeshProUGUI>().color = Color.white;
        }


    }
}
