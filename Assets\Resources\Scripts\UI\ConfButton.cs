using UnityEngine;
using UnityEngine.UI;

public class ConfButton : MonoBehaviour
{
    public GameObject confMenu;

    private Button button;

    // Start is called once before the first execution of Update after the MonoBehaviour is created
    void Start()
    {
        button = GetComponent<Button>();
        button.onClick.AddListener(ActivateConf);
    }

    // Update is called once per frame
    void Update()
    {

    }

    void ActivateConf()
    {
        // play the select sound effect
        ConfMenu.PlaySoundWithVolume(GameObject.Find("GameConfigsHandler").GetComponent<AudioSource>(), Resources.Load<AudioClip>("SFX/MenuSound"));

        if (confMenu.activeSelf) confMenu.SetActive(false);
        else confMenu.SetActive(true);
    }
}
