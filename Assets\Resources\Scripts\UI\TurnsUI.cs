using UnityEngine;
using TMPro;

public class TurnsUI : MonoBehaviour
{
    ConfigsHandler configsHandler; // Reference to the ConfigsHandler

    TextMeshProUGUI text; // Reference to the text

    void Start()
    {
        configsHandler = GameObject.Find("GameConfigsHandler").GetComponent<ConfigsHandler>(); // Get the ConfigsHandler script

        text = transform.GetChild(0).GetComponent<TextMeshProUGUI>(); // Get the text
    }

    void Update()
    {
        switch (gameObject.name) // Set the text based on the name of the object
        {
            case "PlayerTurn": text.text = "Player turn " + configsHandler.playerTurns.ToString(); break;
            case "EnemyTurn": text.text = "Enemy turn " + configsHandler.enemyTurns.ToString(); break;
            default: text.text = "Turns " + configsHandler.turns.ToString(); break;
        }
    }
}
