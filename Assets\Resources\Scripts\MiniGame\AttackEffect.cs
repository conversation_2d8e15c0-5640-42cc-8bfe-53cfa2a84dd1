using UnityEngine;

public class AttackEffect : MonoBehaviour
{
    readonly float rotationSpeed = 12f;
    readonly float frequency = 5f;
    float time = 0f;

    void Update()
    {
        // (sgn(sin(x*p)) * (|sin(x*p)^3| / ((cos(x*p)^2)+0.2)))/2.5 x is Time.time, p is frenquency,
        // this was based on the sine wave equation sin(x*5pi)*2,
        // this make it have a exponetional growth until it reaches y {-2, 2} and then shrink exponational at the same rate that it grow

        transform.localScale = Vector3.one * (Mathf.Sign(Mathf.Sin(time * frequency)) * (Mathf.Abs(Mathf.Pow(Mathf.Sin(frequency * time), 3)) / (Mathf.Pow(Mathf.Cos(frequency * time), 2) + 0.2f)) / 2.5f);

        if(transform.localScale.x <= 0f && time > 0) Destroy(gameObject);

        time += Time.deltaTime;

        transform.rotation *= Quaternion.Euler(0, 0, rotationSpeed * Time.deltaTime * 60f);
    }
}

