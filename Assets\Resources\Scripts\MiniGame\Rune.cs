using UnityEngine;
using UnityEngine.UI;
using UnityEngine.EventSystems;
using System;

public class Rune : <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, IDragHandler, IBeginDragHandler, IEndDragHandler, IPointerDownHandler, IPointerUpHandler
{
    public int x, y;

    ConfigsHandler configsHandler;
    public GridManager gridManager;
    Camera uiCamera;

    public RectTransform visual;
    public Image runeImage;
    public Image visualImage;

    public Types type;

    public bool isDragging = false;
    public bool isSwapping = false;

    bool runeSwapped = false;

    private Vector2Int lastGridPos; // track where the drag was previously
    private bool hasInitializedDrag = false;

    // Start is called once before the first execution of Update after the MonoBehaviour is created
    void Start()
    {
        configsHandler = GameObject.Find("GameConfigsHandler").GetComponent<ConfigsHandler>();
        gridManager = GameObject.Find("GridManager").GetComponent<GridManager>();
        uiCamera = GameObject.Find("UIOverlayCam").GetComponent<Camera>();
    }

    void Update()
    {

    }

    public void Initialize(int x, int y, GridManager gridManager)
    {
        this.x = x;
        this.y = y;
        this.gridManager = gridManager;

        visual.localScale = Vector3.one;
    }

    public void OnBeginDrag(PointerEventData eventData)
    {
        // Disables dragging if board is matching, collapsing and spawning
        if (gridManager.isBoardBusy) return;
        if (configsHandler.ActionMenu.activeSelf) return;
        if (!configsHandler.playerTurn)
        {
            visual.localPosition = Vector3.zero;
            return;
        }

        Vector2 cursorWorldPos = uiCamera.ScreenToWorldPoint(eventData.position);
        Vector2Int pointerGridPos = gridManager.WorldToGrid(cursorWorldPos);

        lastGridPos = pointerGridPos;
        hasInitializedDrag = false;

        isDragging = true;
        gridManager.canCheckMatches = false;
        gridManager.canDestroy = false;

        gridManager.attacks = 0;
        gridManager.combo = 0;

        visualImage.color = new Color(1f, 1f, 1f, 0.0f); // Make visual transparent

        // Ensure dragged visual is rendered on top
        gameObject.transform.SetAsLastSibling();

        RuneOffVisual.Instance.Show(type);



    }

    public void OnDrag(PointerEventData eventData)
    {
        // Disables dragging if board is matching, collapsing and spawning
        if (gridManager.isBoardBusy) return;
        if (configsHandler.ActionMenu.activeSelf) return;
        if (!configsHandler.playerTurn)
        {
            visual.localPosition = Vector3.zero;
            return;
        }
        gameObject.transform.SetAsLastSibling();
        visualImage.color = new Color(1f, 1f, 1f, 0.0f); // Make visual transparent

        Vector2 cursPos = uiCamera.ScreenToWorldPoint(eventData.position);
        Vector2 offsetCursPos = cursPos + Vector2.up * 0.3f; // Offset to avoid visual overlap

        RuneOffVisual.Instance.UpdatePosition(offsetCursPos); // Visual representation of the rune that follows the cursor

        gridManager.canCheckMatches = false;
        gridManager.canDestroy = false;

        Vector2Int pointerGridPos = gridManager.WorldToGrid(cursPos); // Finds what grid cell the cursor is hovering

        // On first drag
        if (!hasInitializedDrag)
        {
            lastGridPos = pointerGridPos;
            hasInitializedDrag = true;
            return;
        }

        // If cursor is still in the same grid cell, do nothing
        if (pointerGridPos == lastGridPos) return;

        Vector2Int direction = pointerGridPos - lastGridPos;
        int steps = Mathf.Max(Mathf.Abs(direction.x), Mathf.Abs(direction.y));
        Vector2Int stepDir = new Vector2Int(Math.Sign(direction.x), Math.Sign(direction.y));

        for (int i = 1; i <= steps; i++)
        {
            Vector2Int intermediatePos = lastGridPos + stepDir * i;
            Rune targetRune = gridManager.GetRuneAt(intermediatePos);

            if (targetRune != null && targetRune != this)
            {
                //gridManager.SwapRunes(this, targetRune);
                gridManager.EnqueueSwap(this, targetRune);
                runeSwapped = true;
                        // Starts energy bar countdown and animation
        StartCoroutine(configsHandler.CountDown(this));
            }
        }

        lastGridPos = pointerGridPos;
    }

    public void OnEndDrag(PointerEventData eventData)
    {
        // Disables dragging if board is matching, collapsing and spawning
        if (gridManager.isBoardBusy) return;
        if (configsHandler.ActionMenu.activeSelf) return;
        if (!configsHandler.playerTurn)
        {
            visual.localPosition = Vector3.zero;
            return;
        }

        isDragging = false;
        gridManager.canCheckMatches = true;
        gridManager.canDestroy = true;

        visual.localPosition = Vector3.zero;
        visualImage.color = new Color(1f, 1f, 1f, 1f); // Reset visual color to fully opaque
        RuneOffVisual.Instance.Hide();

        //Only handle mathes if the rune was swapped
        if (runeSwapped)
        {
            gridManager.HandleMatchesAfterMove(); // <- Trigger match chain
            runeSwapped = false;
        }
        hasInitializedDrag = false;

    }

    public void OnPointerDown(PointerEventData eventData)
    {
        if (configsHandler.ActionMenu.activeSelf) return;
        Vector2 cursPos = uiCamera.ScreenToWorldPoint(eventData.position);
        Vector2 offsetCursPos = cursPos + Vector2.up * 0.3f; // Offset to avoid visual overlap

        RuneOffVisual.Instance.UpdatePosition(offsetCursPos);

        RuneOffVisual.Instance.Show(type);
        visualImage.color = new Color(1f, 1f, 1f, 0.0f);
    }

    public void OnPointerUp(PointerEventData eventData)
    {
        RuneOffVisual.Instance.Hide();

        visualImage.color = new Color(1f, 1f, 1f, 1f);
    }
}
