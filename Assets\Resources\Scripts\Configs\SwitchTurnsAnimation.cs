using System.Collections;
using TMPro;
using UnityEngine;

public class SwitchTurnsAnimation : MonoBehaviour
{
    public Vector3 startPos;
    public float maxTime = Mathf.PI * 2;
    float time = 0f;
    public float maxTimeFraction = 10f;
    TextMeshProUGUI text;

    // Start is called once before the first execution of Update after the MonoBehaviour is created
    void Start()
    {
        text = GetComponent<TextMeshProUGUI>();

        startPos = transform.parent.position;
    }

    public void SwitchTurns(string label, Color textColor)
    {
        StopAllCoroutines();
        StartCoroutine(PlayAnimation(label, textColor));
    }

    public IEnumerator PlayAnimation(string label, Color textColor)
    {
        transform.parent.position = startPos;
        time = 0f;
        text.text = label;
        text.color = textColor;

        Vector2 textSize = text.GetPreferredValues();

        transform.parent.GetComponent<RectTransform>().SetSizeWithCurrentAnchors(RectTransform.Axis.Horizontal, textSize.x + (textSize.x * 0.1f));

        Vector3 endPos = new(startPos.x * -1, startPos.y, startPos.z);

        float distance = Vector3.Distance(startPos, endPos) / 2;
        float speed = distance / (maxTime / 2 - (maxTime / 2 / maxTimeFraction));

        while (time < maxTime)
        {
            if (time < maxTime / 2) transform.parent.position = Vector3.MoveTowards(transform.parent.position, new(0, startPos.y, startPos.z), speed * Time.deltaTime);
            else transform.parent.position = Vector3.MoveTowards(transform.parent.position, endPos, speed * Time.deltaTime);

            time += Time.deltaTime;

            yield return null;
        }

        transform.parent.position = endPos;
    }
}