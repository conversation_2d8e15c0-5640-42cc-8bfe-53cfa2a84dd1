using System.Collections.Generic;
using UnityEngine;

public class RunePool : MonoBeh<PERSON>our
{
    [SerializeField] private GameObject runePrefab;
    [SerializeField] private int poolSize = 30;

    private readonly Queue<GameObject> pool = new Queue<GameObject>();

    void Awake()
    {
        for (int i = 0; i < poolSize; i++)
        {
            GameObject rune = Instantiate(runePrefab, transform);
            rune.SetActive(false);
            pool.Enqueue(rune);
        }
    }

    public GameObject GetRune()
    {
        if (pool.Count > 0)
        {
            GameObject rune = pool.Dequeue();
            rune.SetActive(true);
            return rune;
        }
        else
        {
            GameObject rune = Instantiate(runePrefab, transform);
            return rune;
        }
    }

    public void ReturnRune(GameObject rune)
    {
        rune.SetActive(false);
        pool.Enqueue(rune);
    }
}
