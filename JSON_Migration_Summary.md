# LoadValues.cs JSON Migration Summary

## Overview
Successfully migrated the LoadValues.cs save/load system from custom text format to JSON format while maintaining backward compatibility.

## Files Created

### 1. JsonDataModels.cs
- **JsonSkillValues**: Serializable version of SkillValues
- **JsonCharacterSkills**: Serializable version of CharacterSkills with array-based storage for Types enum
- **JsonCharacterStatus**: Serializable version of CharacterStatus
- **JsonCharacterMods**: Serializable version of CharacterMods
- **JsonBattleCharacter**: Serializable version of BattleCharacter
- **JsonBuffNDebuffs**: Serializable version of BuffNDebuffs
- **JsonPartyCharacters**: Serializable version of PartyCharacters (stores character IDs instead of references)
- **JsonVolumeSettings**: Container for volume settings
- **JsonDamageVariables**: Container for damage calculation variables
- **JsonComboChances**: Container for combo chance arrays
- **JsonGoldenStrike**: Container for golden strike arrays
- **JsonEnergyAmount**: Container for energy timer

### 2. JsonSaveHelper.cs
- **GetSaveFolder()**: Centralized save folder path logic
- **SaveToJson<T>()**: Generic JSON saving method using Unity's JsonUtility
- **LoadFromJson<T>()**: Generic JSON loading method with error handling
- **FileExists()**: File existence check
- **MigrateToJson()**: Migration helper for old formats
- **Convert methods**: Bidirectional conversion between current data structures and JSON models

### 3. JsonSaveTest.cs
- Test script to verify JSON save/load functionality
- Tests volume settings, combo chances, damage variables, and character data
- Can be run via context menu in Unity Editor

## Migration Strategy

### File Format Changes
| Old Format | New Format | Description |
|------------|------------|-------------|
| bonusChances.bcas | bonusChances.json | Combo chance arrays |
| characters.chars | characters.json | Character data |
| damageVariables.davs | damageVariables.json | Damage calculation variables |
| defaultMods.dmos | defaultMods.json | Default character mods |
| defaultSkills.dsks | defaultSkills.json | Default character skills |
| defaultStats.dsts | defaultStats.json | Default character stats |
| goldenStrike.gsts | goldenStrike.json | Golden strike arrays |
| energy.enea | energy.json | Energy timer |
| partyCharacters.pchs | partyCharacters.json | Party configurations |
| skills.skis | skills.json | Buffs and debuffs |
| volumeSettings.vols | volumeSettings.json | Volume settings |

### Backward Compatibility
- **TryLoadFromJson()** method attempts JSON first, falls back to legacy format
- Legacy files are automatically migrated to JSON on first load
- Old files are kept as .bak backups after migration
- All existing save data is preserved during migration

## Key Features

### 1. Automatic Migration
```csharp
private bool TryLoadFromJson<T>(string jsonFileName, string legacyFileName, out T data, Func<string, T> legacyParser)
```
- Tries JSON format first
- Falls back to legacy format if JSON doesn't exist
- Automatically migrates legacy data to JSON
- Preserves original files as backups

### 2. Centralized Save/Load Logic
- All JSON operations go through JsonSaveHelper
- Consistent error handling and logging
- Unified save folder management

### 3. Type-Safe Serialization
- All data models use Unity's [Serializable] attribute
- Proper handling of complex types like Dictionary<Types, SkillValues>
- Character references stored as IDs for proper serialization

## Updated LoadValues.cs Methods

### Loading Methods
- **LoadSaveFiles()**: Updated to use JSON with legacy fallback
- **TryLoadFromJson()**: New method for JSON loading with migration
- **MigrateOldFormatsToJson()**: Handles .dat to custom extension migration

### Saving Methods
- **save()**: Updated to use JSON format for all data
- **SaveVolumeToFile()**: Simplified to use JSON
- All save operations now use JsonSaveHelper.SaveToJson()

## Volume Settings Integration
- Maintains compatibility with ConfMenu.cs centralized volume control
- Volume settings saved immediately on change via SaveVolumeToFile()
- Preserves toggle states and volume levels separately

## Benefits of JSON Migration

### 1. Readability
- Human-readable save files for easier debugging
- Clear data structure visible in JSON format
- Easier to inspect and modify save data if needed

### 2. Maintainability
- Simplified serialization logic
- Reduced custom parsing code
- Better error handling and logging

### 3. Extensibility
- Easy to add new fields to data structures
- JSON format naturally handles optional fields
- Better version compatibility for future updates

### 4. Debugging
- Save files can be opened and inspected in any text editor
- Clear data structure makes troubleshooting easier
- JSON validation tools can verify file integrity

## Testing
- JsonSaveTest.cs provides comprehensive testing
- Tests all major data types and conversion methods
- Verifies round-trip serialization accuracy
- Can be run from Unity Editor context menu

## Next Steps
1. Test the migration with existing save files
2. Verify volume persistence works correctly
3. Run comprehensive tests on all data types
4. Validate backward compatibility with old save files
5. Monitor for any performance impacts

## Notes
- Unity's JsonUtility is used for serialization (built-in, no external dependencies)
- All compilation errors are expected until Unity compiles the new classes
- File extensions changed to .json for clarity and consistency
- Original LoadValues.cs API maintained for minimal breaking changes
