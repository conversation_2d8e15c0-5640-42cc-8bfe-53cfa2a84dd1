using UnityEngine;

public class PerspectiveCanvas : MonoBehaviour
{
    public Camera perspectiveCamera;
    public float distanceFromCamera = 1f;
    public Vector2 referenceResolution = new Vector2(1080, 1920);

    void Start()
    {
        MatchScale();
    }

    void MatchScale()
    {
        float fov = perspectiveCamera.fieldOfView;
        float aspect = perspectiveCamera.aspect;

        float canvasHeight = 2f * distanceFromCamera * Mathf.Tan(fov * 0.5f * Mathf.Deg2Rad);
        float canvasWidth = canvasHeight * aspect;

        float scaleX = canvasWidth / referenceResolution.x;
        float scaleY = canvasHeight / referenceResolution.y;
        float uniformScale = Mathf.Min(scaleX, scaleY); // Use the smaller value to maintain fit

        transform.localScale = new Vector3(uniformScale, uniformScale, uniformScale);
        transform.position = perspectiveCamera.transform.position + perspectiveCamera.transform.forward * distanceFromCamera;
        transform.rotation = perspectiveCamera.transform.rotation;
    }
}

