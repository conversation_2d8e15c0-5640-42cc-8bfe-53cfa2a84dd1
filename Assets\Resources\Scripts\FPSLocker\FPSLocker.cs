using UnityEngine;
using UnityEngine.InputSystem;
using UnityEngine.Windows;

public class FpsLocker : MonoBehaviour
{
    [Range(5, 60)]
    public int fpsLimit = 60;

    public bool LimitFPS = false;

    private int defaultFPS;

    void Start()
    {
    defaultFPS = -1;
    // Always disable VSync on Android
    QualitySettings.vSyncCount = 0;
    
    // Set target framerate to 60 by default on Android
    Application.targetFrameRate = 60;
    
    // Then apply any custom limits if enabled
        if (LimitFPS)
        {
            QualitySettings.vSyncCount = 0;
            Application.targetFrameRate = fpsLimit;
        }
    }

    private void Update()
    {
        if (Keyboard.current.f12Key.wasPressedThisFrame) LimitFPS = !LimitFPS;

        if (LimitFPS && Application.targetFrameRate != fpsLimit) Application.targetFrameRate = fpsLimit;
        else if (!LimitFPS) Application.targetFrameRate = defaultFPS;
    }
}
