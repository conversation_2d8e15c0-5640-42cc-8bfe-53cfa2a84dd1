using UnityEngine;

public class InverteSide : MonoBehaviour
{
    ConfigsHandler configsHandler; // Reference to the ConfigsHandler

    void Start()
    {
        configsHandler = GameObject.Find("GameConfigsHandler").GetComponent<ConfigsHandler>(); // Get the ConfigsHandler script
    }

    void Update() // invert the side of the UI depending on the selected player
    {
        transform.position = transform.parent.position + new Vector3(1.512f * (configsHandler.GetSelectedPlayer() < 2 ? 1: -1), 0f, 0f);
    }
}
