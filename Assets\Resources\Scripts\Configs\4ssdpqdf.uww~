using System.Collections.Generic;
using TMPro;
using UnityEngine;
using UnityEngine.UI;

public class StatsValueDisplay : MonoBehaviour
{
    public GameObject apmin, bl, hp, atk, def, atkLim;

    float scrollSpeed = 5f;

    float pos = 0.45f;

    CharConfUI confUI;

    Button pasteButton;

    private void Start()
    {
        pasteButton = GameObject.Find("PasteButton").GetComponent<Button>();

        pasteButton.onClick.AddListener(PasteValues);
    }

    private void Update()
    {
        Camera confCamera = GameObject.Find("Configs Camera").GetComponent<Camera>();

        if (confCamera != null && confCamera.enabled)
        {

            if (Input.mousePosition.x / confCamera.pixelWidth > pos)
            {
                Vector3 movement = new Vector3(0f, Input.GetAxis("Mouse ScrollWheel") * scrollSpeed, 0f);

                transform.Translate(movement);
            }
        }
    }

    void PasteValues()
    {
        string clipboardText = GUIUtility.systemCopyBuffer;
        if(clipboardText.Length > 0) 
        {
            int s = 0;
            Debug.Log(clipboardText); 
            string[] values = clipboardText.Split('\n');

            List<int> valuesList = new();

            if (values[0].Split("\t").Length < 6) s = 1;
            for(int i = 0; i < values.Length; i++)
            { 
                Debug.Log(value); 
            }
        }
    }


    public void SetValues(BattleCharacter character, CharConfUI confUI)
    {
        this.confUI = confUI;

        apmin.GetComponent<TextMeshProUGUI>().text = "Apmin\n" + string.Join("\n", character.stats.GetApMin());
        bl.GetComponent<TextMeshProUGUI>().text = "BL\n" + string.Join("\n", character.stats.GetBl());
        hp.GetComponent<TextMeshProUGUI>().text = "HP\n" + string.Join("\n", character.stats.GetHp());
        atk.GetComponent<TextMeshProUGUI>().text = "Atk\n" + string.Join("\n", character.stats.GetAtk());
        def.GetComponent<TextMeshProUGUI>().text = "Def\n" + string.Join("\n", character.stats.GetDef());
        atkLim.GetComponent<TextMeshProUGUI>().text = "AtkLim\n" + string.Join("\n", character.stats.GetAtkLim());
    }
}
