using UnityEngine;
using UnityEngine.UI;

public class RollsLeft : MonoBehaviour
{

    void Update()
    {
        int dicesUsed = transform.parent.GetComponent<CalculateChances>().rollAttempts;

        foreach (var useLeft in transform.GetComponentsInChildren<Image>())
        {
            if (useLeft.transform.GetSiblingIndex() < dicesUsed) useLeft.color = new(1, 1, 1, 0.3f);
            else useLeft.color = Color.white;
        }
    }
}
