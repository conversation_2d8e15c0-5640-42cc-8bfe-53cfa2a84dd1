//this enum is a list of all types avalable, the game scales with them, for every new type,
//is recommended to add the sprite in the Assets/Resources/Sprites with the exact same name,
//the game will run still work without the sprite, but the objects that try to use that type will have the default unity sprite, that is a white square

public enum Types
{
    Strength,
    Magic,
    Fire,
    Venom,
    Possession,
    Electricity,
    Acid,
    Frost
}
