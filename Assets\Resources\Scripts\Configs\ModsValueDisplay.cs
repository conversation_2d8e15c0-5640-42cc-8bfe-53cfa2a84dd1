using TMPro;
using UnityEngine;
using UnityEngine.UI;

public class ModsValueDisplay : MonoBehaviour
{
    public GameObject knowledge, luck, speed, precision, evasion, criticalChance, parryChance; // GameObject of each mod

    CharConfUI confUI; // Reference to the CharConfUI

    TMP_InputField knowledgeInput, luckInput, speedInput, precisionInput, evasionInput, criticalChanceInput, parryChanceInput; // Reference to the input fields

    void Start()
    {
        // Find the input fields
        knowledgeInput = knowledge.GetComponent<TMP_InputField>();
        luckInput = luck.GetComponent<TMP_InputField>();
        speedInput = speed.GetComponent<TMP_InputField>();
        precisionInput = precision.GetComponent<TMP_InputField>();
        evasionInput = evasion.GetComponent<TMP_InputField>();
        criticalChanceInput = criticalChance.GetComponent<TMP_InputField>();
        parryChanceInput = parryChance.GetComponent<TMP_InputField>();

        // Add listeners to the input fields
        knowledgeInput.onEndEdit.AddListener(delegate { confUI.ChangeMods("knowledge", int.Parse(knowledgeInput.text)); });
        luckInput.onEndEdit.AddListener(delegate { confUI.ChangeMods("luck", int.Parse(luckInput.text)); });
        speedInput.onEndEdit.AddListener(delegate { confUI.ChangeMods("speed", int.Parse(speedInput.text)); });
        precisionInput.onEndEdit.AddListener(delegate { confUI.ChangeMods("precision", int.Parse(precisionInput.text)); });
        evasionInput.onEndEdit.AddListener(delegate { confUI.ChangeMods("evasion", int.Parse(evasionInput.text)); });
        criticalChanceInput.onEndEdit.AddListener(delegate { confUI.ChangeMods("criticalChance", int.Parse(criticalChanceInput.text)); });
        parryChanceInput.onEndEdit.AddListener(delegate { confUI.ChangeMods("parryChance", int.Parse(parryChanceInput.text)); });

    }

    void PasteValues() // Paste the values
    {
        if (GameObject.Find("ModsValues") == null) return; // if the game object that contains the mods values is deactivated it returns

        string clipboardText = GUIUtility.systemCopyBuffer; // Get the clipboard
        if (clipboardText.Length > 0) // If the clipboard is not empty
        {
            string[] values = clipboardText.Split('\n'); // Split the clipboard

            string[] modTypes = { "knowledge", "luck", "speed", "precision", "evasion", "criticalChance" }; // Types of mods

            if (values[0].Split("\t").Length > 1) return; // returns if the clipboard has more than one column

            for (int i = 0; i < values.Length - 1; i++) // passes through the values
            {
                if (i >= modTypes.Length) break; // returns if the index is greater than the length of the mod types

                confUI.ChangeMods(modTypes[i], int.Parse(values[i])); // changes the mod
            }
        }
    }
    void Randomize() => confUI.RandomizeMods();

    public void SetValues(BattleCharacter character, CharConfUI confUI) // updates the values on the screen
    {
        this.confUI = confUI;

        knowledgeInput.text = character.mods.GetKnowledge().ToString();
        luckInput.text = character.mods.GetLuck().ToString();
        speedInput.text = character.mods.GetSpeed().ToString();
        precisionInput.text = character.mods.GetPrecision().ToString();
        evasionInput.text = character.mods.GetEvasion().ToString();
        criticalChanceInput.text = character.mods.GetCriticalChance().ToString();
        parryChanceInput.text = character.mods.GetParryChance().ToString();
    }
}
