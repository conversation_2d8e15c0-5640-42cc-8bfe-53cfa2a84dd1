using TMPro;
using UnityEngine;

public class PPDisplay : MonoBehaviour
{
    ConfigsHandler configsHandler; // Reference to the ConfigsHandler

    public GameObject ppDisplay;

    float maxPP;
    float ppLeft;
    TextMeshProUGUI ppNumber;

    // Start is called once before the first execution of Update after the MonoBehaviour is created
    void Start()
    {
        configsHandler = GameObject.Find("GameConfigsHandler").GetComponent<ConfigsHandler>(); // Get the ConfigsHandler script
        ppNumber = ppDisplay.transform.GetChild(0).GetComponent<TextMeshProUGUI>(); // Get the TextMeshProUGUI component
    }

    // Update is called once per frame
    void Update()
    {
        UpdatePP();
    }

    void UpdatePP() // Update the PP display
    {
        ppLeft = configsHandler.ppLeft;
        maxPP = configsHandler.MaxPP;
        ppNumber.text = ppLeft + "/" + maxPP;
    }
}
