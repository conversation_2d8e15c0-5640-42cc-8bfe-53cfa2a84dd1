/// <summary>
/// Represents a party of characters.
/// </summary>
public class PartyCharacters
{
    /// <summary>
    /// Party's name
    /// </summary>
    public string name;

    /// <summary>
    /// Active characters in the party
    /// </summary>
    public BattleCharacter[] activeCharacters = new BattleCharacter[4];

    /// <summary>
    /// Stock characters in the party
    /// </summary>
    public BattleCharacter[] stockCharacters = new BattleCharacter[12];

    /// <summary>
    /// Initializates a new instance of the <see cref="PartyCharacters"/> class with the name, active characters and stock characters specified.
    /// </summary>
    /// <param name="name">Party's name.</param>
    /// <param name="activeCharacters">Active characters in the party.</param>
    /// <param name="stockCharacters">Stock characters in the party.</param>
    public PartyCharacters(string name, BattleCharacter[] activeCharacters, BattleCharacter[] stockCharacters)
    {
        this.name = name;
        this.activeCharacters = activeCharacters;
        this.stockCharacters = stockCharacters;
    }

    /// <summary>
    /// Initializates a new instance of the <see cref="PartyCharacters"/> class with the name specified.
    /// </summary>
    /// <param name="name">Party's name.</param>
    public PartyCharacters(string name)
    {
        this.name = name;
    }

    /// <summary>
    /// Initializates a new instance of the <see cref="PartyCharacters"/> class with the active characters and stock characters specified.
    /// </summary>
    /// <param name="activeCharacters">Active characters in the party.</param>
    /// <param name="stockCharacters">Stock characters in the party.</param>
    public PartyCharacters(BattleCharacter[] activeCharacters, BattleCharacter[] stockCharacters)
    {
        name = "name";
        this.activeCharacters = activeCharacters;
        this.stockCharacters = stockCharacters;
    }
}