using System.Collections;
using UnityEngine;
using UnityEngine.UI;

public class Tile
{
    public GameObject tile = new(); // the rune/tile itself
    public GameObject tileImg = new(); // its sprite

    // information about the tile
    public bool isDragged = false;
    public bool isDestroyed = false;
    public Types type;
    public int id;

    // start position of the tile
    private float startPosY;

    // as the name suggest, it creates a tile, i made it like this because i didnt knew how prefab works, might be better to use prefab to compact the code
    public void CreateTile(float x, float y, Types type, float startPosY, GameObject creator)
    {
        this.startPosY = startPosY; // saves the start position of the grid, it starts from the left top corner
        this.type = type; // sets its type, so is easier to make a full clear of the grid and have the whole system work without any problems
        
        tile.AddComponent<RectTransform>(); // adds the rect transform>
        tile.transform.SetParent(creator.transform, false); // makes the tile a child of the grid
        tileImg.transform.SetParent(tile.transform, false); // makes the image a child of the tile
        tile.name = type.ToString(); // gives the tile a name
        tile.layer = LayerMask.NameToLayer("TileLayer"); // sets the layer of the tile
        tile.transform.position = new Vector3(x, -y, 0); // sets the position of the tile
        tileImg.AddComponent<Image>().sprite = Resources.Load<Sprite>($@"Sprites/UI/{type}"); // sets the sprite of the tile

        tile.AddComponent<CircleCollider2D>().isTrigger = true; // makes the tile a trigger
        tile.GetComponent<CircleCollider2D>().radius = 45f; // sets the radius of the collider

        var rect = tile.GetComponent<RectTransform>(); // gets the rect transform of the tile

        // original size 0.65,0.65 cellsize 0.80
        rect.localScale = new Vector3(0.05f, 0.05f, 1f); // sets the scale of the tile

        tile.AddComponent<Rigidbody2D>().bodyType = RigidbodyType2D.Kinematic; // makes the tile kinematic

        tile.AddComponent<TileCollisionHandler>().Initialize(this); // adds the tile collision handler
    }

    // updates the tile position
    public IEnumerator UpdateTile(Vector3 endPos, float speed = 0.8f / 96)
    {
        // hides the tile if is abit more than the start position, kinda buggy right now,
        // for some reason, there times that it doesn't reactivate it, but seems to only happen at the start of the scene
        // it could be fixed if it was checking every frame instead of when the animation was happening
        // still, i don't know why that happens, because this can only be called when the tiles are destroyed
        if(tile.transform.position.y > startPosY + 1f) tile.SetActive(false);
        else tile.SetActive(true);

        if (tile.transform.position != endPos) // moves the tile to the currect position if its not there
        {
            tile.transform.position = Vector3.MoveTowards(tile.transform.position, endPos, speed);
            if (tile.transform.position.y < endPos.y) tile.transform.position = endPos;
        }

        yield return null;
    }
}