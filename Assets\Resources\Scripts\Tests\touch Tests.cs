using TMPro;
using UnityEngine;

[System.Diagnostics.CodeAnalysis.SuppressMessage("Major Code Smell", "S125:Sections of code should not be commented out", Justification = "<Pendente>")]
public class touchTests : MonoBehaviour
{
    // this script isn't important anymore, but if removed,
    // remove the disabled GameObject called "test" from the Main scene

    TextMeshPro text;
    Camera confCamera;

    // Start is called once before the first execution of Update after the MonoBehaviour is created
    void Start()
    {
        text = GetComponent<TextMeshPro>();
        confCamera = GameObject.Find("Configs Camera").GetComponent<Camera>();
    }

    // Update is called once per frame
    void Update()
    {
        text.text = "Folder path: " + Application.persistentDataPath;
            //"Touch count: " + Input.touchCount.ToString() + 
            //"\nIs touching: " + (Input.touchCount > 0 && (Input.GetTouch(0).phase == TouchPhase.Began || Input.GetTouch(0).phase == TouchPhase.Moved || Input.GetTouch(0).phase == TouchPhase.Stationary)).ToString() + 
            //"\nTouch Position: " + (Input.touchCount > 0 && confCamera.isActiveAndEnabled ? confCamera.ScreenToViewportPoint(Input.GetTouch(0).position).ToString() : Vector3.zero.ToString());
    }
}
