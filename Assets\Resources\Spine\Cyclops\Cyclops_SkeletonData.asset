%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f1b3b4b945939a54ea0b23d3396115fb, type: 3}
  m_Name: Cyclops_SkeletonData
  m_EditorClassIdentifier: 
  atlasAssets:
  - {fileID: 11400000, guid: 2b03c824002b333409a7279f00f94f86, type: 2}
  scale: 0.01
  skeletonJSON: {fileID: 4900000, guid: 9daa3669929ab244eba7e01bd67ae7dd, type: 3}
  isUpgradingBlendModeMaterials: 0
  blendModeMaterials:
    requiresBlendModeMaterials: 1
    applyAdditiveMaterial: 1
    additiveMaterials:
    - pageName: Cyclops.png
      material: {fileID: 2100000, guid: e1970838196f6d94499e60aa49763400, type: 2}
    multiplyMaterials:
    - pageName: Cyclops.png
      material: {fileID: 2100000, guid: 95a43d58dc5ee9542a60e80f14051c31, type: 2}
    screenMaterials:
    - pageName: Cyclops.png
      material: {fileID: 2100000, guid: 77405d69ad4474048b59cd66a600a7f7, type: 2}
  skeletonDataModifiers: []
  fromAnimation: []
  toAnimation: []
  duration: []
  defaultMix: 0.2
  controller: {fileID: 0}
