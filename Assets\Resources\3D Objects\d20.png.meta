fileFormatVersion: 2
guid: a1a72c07fe09b9f4eabd3e7a2e9685ac
TextureImporter:
  internalIDToNameTable:
  - first:
      213: 3913673683237085870
    second: d20 1_0
  - first:
      213: -248253763287697028
    second: d20 1_1
  - first:
      213: 5107839753475979007
    second: d20 1_2
  - first:
      213: -4779008343270671187
    second: d20 1_3
  - first:
      213: 6765287349498429387
    second: d20 1_4
  - first:
      213: 1011646146127599762
    second: d20 1_5
  - first:
      213: -2549506753007287028
    second: d20 1_6
  - first:
      213: -8299440708891599781
    second: d20 1_7
  - first:
      213: -3424786920731158263
    second: d20 1_8
  - first:
      213: 7179597667185826503
    second: d20 1_9
  - first:
      213: 8065241669247880136
    second: d20 1_10
  - first:
      213: 2952515728294970905
    second: d20 1_11
  - first:
      213: -6114041556293009974
    second: d20 1_12
  - first:
      213: -2273560117081637275
    second: d20 1_13
  - first:
      213: 7543165124157552385
    second: d20 1_14
  - first:
      213: -4570023409191565261
    second: d20 1_15
  - first:
      213: -785740086222250061
    second: d20 1_16
  - first:
      213: -1868312686839243923
    second: d20 1_17
  - first:
      213: 2338273303342434806
    second: d20 1_18
  externalObjects: {}
  serializedVersion: 13
  mipmaps:
    mipMapMode: 0
    enableMipMap: 0
    sRGBTexture: 1
    linearTexture: 0
    fadeOut: 0
    borderMipMap: 0
    mipMapsPreserveCoverage: 0
    alphaTestReferenceValue: 0.5
    mipMapFadeDistanceStart: 1
    mipMapFadeDistanceEnd: 3
  bumpmap:
    convertToNormalMap: 0
    externalNormalMap: 0
    heightScale: 0.25
    normalMapFilter: 0
    flipGreenChannel: 0
  isReadable: 0
  streamingMipmaps: 0
  streamingMipmapsPriority: 0
  vTOnly: 0
  ignoreMipmapLimit: 0
  grayScaleToAlpha: 0
  generateCubemap: 6
  cubemapConvolution: 0
  seamlessCubemap: 0
  textureFormat: 1
  maxTextureSize: 2048
  textureSettings:
    serializedVersion: 2
    filterMode: 1
    aniso: 1
    mipBias: 0
    wrapU: 1
    wrapV: 1
    wrapW: 1
  nPOTScale: 0
  lightmap: 0
  compressionQuality: 50
  spriteMode: 2
  spriteExtrude: 1
  spriteMeshType: 1
  alignment: 0
  spritePivot: {x: 0.5, y: 0.5}
  spritePixelsToUnits: 100
  spriteBorder: {x: 0, y: 0, z: 0, w: 0}
  spriteGenerateFallbackPhysicsShape: 1
  alphaUsage: 1
  alphaIsTransparency: 1
  spriteTessellationDetail: -1
  textureType: 8
  textureShape: 1
  singleChannelComponent: 0
  flipbookRows: 1
  flipbookColumns: 1
  maxTextureSizeSet: 0
  compressionQualitySet: 0
  textureFormatSet: 0
  ignorePngGamma: 0
  applyGammaDecoding: 0
  swizzle: 50462976
  cookieLightType: 0
  platformSettings:
  - serializedVersion: 4
    buildTarget: DefaultTexturePlatform
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 4
    buildTarget: Standalone
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 4
    buildTarget: Android
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 4
    buildTarget: iOS
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  spriteSheet:
    serializedVersion: 2
    sprites:
    - serializedVersion: 2
      name: d20 1_0
      rect:
        serializedVersion: 2
        x: 0
        y: 839
        width: 184
        height: 176
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: ea22428ba79205630800000000000000
      internalID: 3913673683237085870
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: d20 1_1
      rect:
        serializedVersion: 2
        x: 185
        y: 841
        width: 202
        height: 176
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: c79351184860e8cf0800000000000000
      internalID: -248253763287697028
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: d20 1_2
      rect:
        serializedVersion: 2
        x: 386
        y: 839
        width: 403
        height: 178
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: ff6fd8b7131b2e640800000000000000
      internalID: 5107839753475979007
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: d20 1_3
      rect:
        serializedVersion: 2
        x: 790
        y: 841
        width: 203
        height: 175
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: dac39b86b3d8dadb0800000000000000
      internalID: -4779008343270671187
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: d20 1_4
      rect:
        serializedVersion: 2
        x: 0
        y: 648
        width: 203
        height: 178
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: bcf3c46100123ed50800000000000000
      internalID: 6765287349498429387
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: d20 1_5
      rect:
        serializedVersion: 2
        x: 240
        y: 651
        width: 202
        height: 176
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 298145a5fc61a0e00800000000000000
      internalID: 1011646146127599762
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: d20 1_6
      rect:
        serializedVersion: 2
        x: 474
        y: 651
        width: 203
        height: 175
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: c058a82bd155e9cd0800000000000000
      internalID: -2549506753007287028
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: d20 1_7
      rect:
        serializedVersion: 2
        x: 712
        y: 648
        width: 207
        height: 181
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: b5c9918c5a672dc80800000000000000
      internalID: -8299440708891599781
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: d20 1_8
      rect:
        serializedVersion: 2
        x: 0
        y: 455
        width: 203
        height: 178
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 9095a6ed076b870d0800000000000000
      internalID: -3424786920731158263
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: d20 1_9
      rect:
        serializedVersion: 2
        x: 257
        y: 457
        width: 202
        height: 176
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 7ca95d60a0e03a360800000000000000
      internalID: 7179597667185826503
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: d20 1_10
      rect:
        serializedVersion: 2
        x: 489
        y: 455
        width: 203
        height: 178
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 8cbc233119e7def60800000000000000
      internalID: 8065241669247880136
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: d20 1_11
      rect:
        serializedVersion: 2
        x: 717
        y: 455
        width: 201
        height: 176
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 91686ca696179f820800000000000000
      internalID: 2952515728294970905
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: d20 1_12
      rect:
        serializedVersion: 2
        x: 0
        y: 262
        width: 200
        height: 176
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: acdbd2f199f862ba0800000000000000
      internalID: -6114041556293009974
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: d20 1_13
      rect:
        serializedVersion: 2
        x: 227
        y: 260
        width: 203
        height: 178
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 56e7add8e11b270e0800000000000000
      internalID: -2273560117081637275
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: d20 1_14
      rect:
        serializedVersion: 2
        x: 486
        y: 260
        width: 203
        height: 178
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 107b68b07b4bea860800000000000000
      internalID: 7543165124157552385
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: d20 1_15
      rect:
        serializedVersion: 2
        x: 715
        y: 260
        width: 204
        height: 178
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 338caef1ce30490c0800000000000000
      internalID: -4570023409191565261
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: d20 1_16
      rect:
        serializedVersion: 2
        x: 0
        y: 72
        width: 170
        height: 178
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 3bf10fafd8d7815f0800000000000000
      internalID: -785740086222250061
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: d20 1_17
      rect:
        serializedVersion: 2
        x: 226
        y: 74
        width: 204
        height: 178
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: d6b5793928b6216e0800000000000000
      internalID: -1868312686839243923
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: d20 1_18
      rect:
        serializedVersion: 2
        x: 486
        y: 74
        width: 201
        height: 174
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 6f941814e27337020800000000000000
      internalID: 2338273303342434806
      vertices: []
      indices: 
      edges: []
      weights: []
    outline: []
    customData: 
    physicsShape: []
    bones: []
    spriteID: 
    internalID: 0
    vertices: []
    indices: 
    edges: []
    weights: []
    secondaryTextures: []
    spriteCustomMetadata:
      entries: []
    nameFileIdTable:
      d20 1_0: 3913673683237085870
      d20 1_1: -248253763287697028
      d20 1_10: 8065241669247880136
      d20 1_11: 2952515728294970905
      d20 1_12: -6114041556293009974
      d20 1_13: -2273560117081637275
      d20 1_14: 7543165124157552385
      d20 1_15: -4570023409191565261
      d20 1_16: -785740086222250061
      d20 1_17: -1868312686839243923
      d20 1_18: 2338273303342434806
      d20 1_2: 5107839753475979007
      d20 1_3: -4779008343270671187
      d20 1_4: 6765287349498429387
      d20 1_5: 1011646146127599762
      d20 1_6: -2549506753007287028
      d20 1_7: -8299440708891599781
      d20 1_8: -3424786920731158263
      d20 1_9: 7179597667185826503
  mipmapLimitGroupName: 
  pSDRemoveMatte: 0
  userData: 
  assetBundleName: 
  assetBundleVariant: 
