using UnityEngine;
using TMPro;
using DG.Tweening;
using UnityEngine.UI;
using System.Collections;

public class DmgLabel : MonoBehaviour
{
    ConfigsHandler configsHandler;

    readonly float jumpHeight = 300f;
    readonly float jumpDuration = 1f;
    readonly float horizontalDistance = 150f;
    readonly float fallBelowOffset = 400f; // how far below it falls
    readonly float fadeDelay = 0.2f;           // When to start fading
    readonly float fadeDuration = 0.6f;

    readonly float floatDistance = 150f;
    readonly float duration = 1.5f;

    private bool isPlayerTurn;
    private float direction;

    private CanvasGroup canvasGroup;
    private RectTransform rectTransform;

    void Start()
    {
        configsHandler = GameObject.Find("GameConfigsHandler").GetComponent<ConfigsHandler>();
        isPlayerTurn = configsHandler.playerTurn;

        // UI component references
        canvasGroup = GetComponent<CanvasGroup>();
        rectTransform = GetComponent<RectTransform>();

        // Makes sure dmg<PERSON>abe<PERSON> don't jump out of the screen(at the begining)
        if (configsHandler.attackedPlayer == 0 && !isPlayerTurn)
        {
            direction = Random.Range(2f, 3.5f); // Always Right
        }
        else if (configsHandler.attackedPlayer == 3 && !isPlayerTurn)
        {
            direction = Random.Range(-3.5f, -2f); // Always Left
        }
        else
        {
            direction = Random.Range(-3.5f, 3.5f);
        }

        // Start with 0 scale for scaling up animation
        rectTransform.localScale = Vector3.zero;

        // Start animating
        if (configsHandler.absorbedAttack || configsHandler.repelledAttack || configsHandler.blockedAttack) ShowSpec();
        else if (configsHandler.parriedAttack) ShowParry();
        else if (configsHandler.counterLabel)
        {
            gameObject.transform.GetChild(1).GetComponent<TextMeshProUGUI>().text = KeywordManager.GetWord("COUNTER_ALERT");
            ShowCounter();
        }
        else StartArcAnimation();
    }

    void Update()
    {

    }

    void StartArcAnimation()
    {
        float targetScale = GetScaledSize(configsHandler.damageNumber);
        Vector2 startPos = rectTransform.anchoredPosition;

        // Calculate horizontal and vertical targets
        float xOffset = direction * horizontalDistance;
        float yOffset = jumpHeight;

        // Scale pop-in
        Sequence scaleSeq = DOTween.Sequence();
        scaleSeq.Append(rectTransform.DOScale(targetScale * 1.2f, 0.35f));
        //scaleSeq.Append(rectTransform.DOScale(targetScale, 0.2f).SetEase(Ease.OutSine));

        // Move X over the full time (constant drift)
        rectTransform.DOAnchorPosX(startPos.x + xOffset, jumpDuration).SetEase(Ease.Linear);

        // Arc Y: jump up then fall down using a Sequence
        Sequence ySeq = DOTween.Sequence();
        ySeq.Append(rectTransform.DOAnchorPosY(startPos.y + yOffset, jumpDuration * 0.5f).SetEase(Ease.OutQuad)); // Jump up
        ySeq.Append(rectTransform.DOAnchorPosY(startPos.y - fallBelowOffset, jumpDuration * 0.7f).SetEase(Ease.InQuad)); // Fall down

        // Fade out while falling
        canvasGroup.DOFade(0f, fadeDuration).SetDelay(fadeDelay + jumpDuration * 0.5f); // start fading during fall

        // Scale down at end
        rectTransform.DOScale(0f, 0.8f).SetDelay(jumpDuration * 0.9f).OnComplete(() =>
        {
            Destroy(gameObject);
        });
    }

    float GetScaledSize(long damage) // Scale down if dmg number too long, to fit in the screen
    {
        // Damage thresholds
        float minDamage = 1;
        float maxDamage = 1e15f; // 1000000000000000

        float minScale = 0.08f;
        float maxScale = 1.0f;

        // Clamp damage between min and max, then invert the scale mapping
        float t = Mathf.InverseLerp(minDamage, maxDamage, damage);
        float scale = Mathf.Lerp(maxScale, minScale, t); // bigger damage → smaller scale

        return scale;
    }

    void ShowSpec() // Show different animation for ABSORVED, REPELLED and BLOCKED label
    {
        canvasGroup.alpha = 1f;

        Vector2 startPos = rectTransform.anchoredPosition;

        // Scale pop-in
        Sequence scaleSeq = DOTween.Sequence();
        scaleSeq.Append(rectTransform.DOScale(1.2f, 0.35f).SetEase(Ease.OutBack));
        //scaleSeq.Append(rectTransform.DOScale(1, 0.2f).SetEase(Ease.OutSine));

        // Random horizontal offset (left or right)
        float randomX = Random.Range(-100f, 100f);

        // Move up + random left/right
        Vector2 endPos = startPos + new Vector2(randomX, floatDistance);

        rectTransform.DOAnchorPos(endPos, duration).SetEase(Ease.OutCubic);
        // Fade out
        canvasGroup.DOFade(0f, duration).SetEase(Ease.InOutSine).OnComplete(() => Destroy(gameObject));

    }

    void ShowParry()
    {
        Time.timeScale = 0.3f;

        canvasGroup.alpha = 0f;
        rectTransform.localScale = Vector3.zero; // Start tiny

        Sequence stampSeq = DOTween.Sequence();
        stampSeq.SetUpdate(true); // Make the whole sequence use unscaled time

        // Instantly enable
        stampSeq.AppendCallback(() =>
        {
            canvasGroup.alpha = 1f;
        });

        // Stamp pop-in
        stampSeq.Append(rectTransform.DOScale(2f, 0.08f)
            .SetEase(Ease.OutBack)
            .SetUpdate(true)); // Optional here, since sequence already has SetUpdate(true)

        stampSeq.Append(rectTransform.DOScale(1.5f, 0.03f)
            .SetEase(Ease.InCubic)
            .SetUpdate(true));

        // Aggressive vibrating shake with dynamic axis variation
        stampSeq.Append(rectTransform.DOShakeScale(
            duration: 0.4f,
            strength: new Vector3(0.6f, 1.6f, 0.6f), // different values per axis
            vibrato: 60,
            randomness: 90,
            fadeOut: true,
            randomnessMode: ShakeRandomnessMode.Harmonic // Optional: adds smoothness
        ).SetUpdate(true));

        // Hold before fading
        stampSeq.AppendInterval(0.2f);

        // Fade and destroy
        stampSeq.Append(canvasGroup.DOFade(0f, 0.2f)
            .SetEase(Ease.InCubic)
            .SetUpdate(true));

        stampSeq.OnComplete(() => { Destroy(gameObject); });
        configsHandler.ScreenShake(0.5f, 0.5f);

        // Restore time after short delay using unscaled time
        StartCoroutine(RestoreTimeAfterDelay(0.5f));
    }

    private IEnumerator RestoreTimeAfterDelay(float delay)
    {
        yield return new WaitForSecondsRealtime(delay); // Use unscaled time
        Time.timeScale = 1f;
        DOTween.Kill(gameObject);
        //Destroy(gameObject);
    }

    void ShowCounter()
    {
        canvasGroup.alpha = 0f;
        rectTransform.localScale = Vector3.zero; // Start tiny

        Sequence stampSeq = DOTween.Sequence();

        // Instantly enable
        stampSeq.AppendCallback(() =>
        {
            canvasGroup.alpha = 1f;
        });

        // Stamp pop-in
        stampSeq.Append(rectTransform.DOScale(2f, 0.08f).SetEase(Ease.OutBack));
        stampSeq.Append(rectTransform.DOScale(1.5f, 0.03f).SetEase(Ease.InCubic));

        // Aggressive vibrating shake
        stampSeq.Append(rectTransform.DOShakeScale(
            duration: 0.25f,
            strength: 0.15f,
            vibrato: 50,           // HIGH frequency
            randomness: 90,
            fadeOut: true
        ));

        // Hold before fading
        stampSeq.AppendInterval(0.2f);

        // Fade and destroy
        stampSeq.Append(canvasGroup.DOFade(0f, 0.2f).SetEase(Ease.InCubic));
        stampSeq.OnComplete(() => Destroy(gameObject));
    }
}
