using System.Collections.Generic;
using TMPro;
using UnityEngine;
using UnityEngine.UI;

public class MoonPhasesConf : MonoBehaviour
{
    public MoonPhases currentMoonPhase;
    private Dictionary<MoonPhases, MoonPhaseData> phaseData;

    public GameObject moonIcon;
    Image moonIconImage;
    public TextMeshProUGUI moonName;
    public TextMeshProUGUI PartyDamageMultiplier;
    public TextMeshProUGUI EnemyDamageMultiplier;

    public Button nextMR;

    private void Awake()
    {
        phaseData = new Dictionary<MoonPhases, MoonPhaseData>()
        {
            { MoonPhases.Nova, new MoonPhaseData("MMRANG32") },
            { MoonPhases.Crescente, new MoonPhaseData("MMRANG33") },
            { MoonPhases.QuartoCrescente, new MoonPhaseData("MMRANG34") },
            { MoonPhases.GibosaCrescente, new MoonPhaseData("MMRANG35") },
            { MoonPhases.LuaCheia, new MoonPhaseData("MMRANG36") },
            { MoonPhases.GibosaMinguante, new MoonPhaseData("MMRANG37") },
            { MoonPhases.QuartoMinguante, new MoonPhaseData("MMRANG38") },
            { MoonPhases.Minguante, new MoonPhaseData("MMRANG39") }
        };
    }

    // Start is called once before the first execution of Update after the MonoBehaviour is created
    void Start()
    {
        currentMoonPhase = MoonPhases.Nova;
        moonIconImage = moonIcon.GetComponent<Image>();
        nextMR.onClick.AddListener(AdvanceMoonPhase);
        //Debug.Log("Party damage: " + GetCurrentPartyDamageMultiplier() + " Enemy damage: " + GetCurrentEnemyDamageMultiplier());
    }

    // Update is called once per frame
    void Update()
    {
        moonIconImage.sprite = Resources.Load<Sprite>($"Sprites/UI/{GeneralInfo.GetMoonPhaseTechnicalName(GetCurrentMoonPhaseId())}");
        moonName.text = GeneralInfo.GetMoonPhaseName(GetCurrentMoonPhaseId())[3..];
        PartyDamageMultiplier.text = GetCurrentPartyDamageMultiplier().ToString();
        EnemyDamageMultiplier.text = GetCurrentEnemyDamageMultiplier().ToString();
    }

    public void SetMoonPhase(MoonPhases moonPhase)
    {
        this.currentMoonPhase = moonPhase;
    }

    public void AdvanceMoonPhase()
    {
        // Advances the moon phase, looping back to the first phase after the last one
        currentMoonPhase = (MoonPhases)(((int)currentMoonPhase + 1) % System.Enum.GetValues(typeof(MoonPhases)).Length);
        //Debug.Log("Moon phase advanced to: " + currentMoonPhase);
        //Debug.Log("Party damage: " + GetCurrentPartyDamageMultiplier() + " Enemy damage: " + GetCurrentEnemyDamageMultiplier());
    }

    public string GetCurrentMoonPhaseId()
    {
        return phaseData[currentMoonPhase].id;
    }

    public float GetCurrentPartyDamageMultiplier()
    {
        return phaseData[currentMoonPhase].partyDamageMultiplier;
    }

    public float GetCurrentEnemyDamageMultiplier()
    {
        return phaseData[currentMoonPhase].enemyDamageMultiplier;
    }
}
