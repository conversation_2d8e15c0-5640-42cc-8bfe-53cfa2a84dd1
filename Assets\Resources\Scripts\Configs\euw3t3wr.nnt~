using TMPro;
using UnityEngine.UI;
using UnityEngine;
using System.Collections.Generic;

public class SkillsValueDisplay : MonoBehaviour
{
    GameObject RandomButton, PasteButton;

    List<SkillValuesInputs> inputs = new();

    CharConfUI confUI;

    void Start()
    {
        GameObject configCanvas = GameObject.Find("ConfigCanvas");

        RandomButton = transform.GetChild(0).gameObject;
        PasteButton = GameObject.Find("PasteButton");

        //int index = 0;

        for(int i = 0; i < System.Enum.GetValues(typeof(Types)).Length; i++)
        {
            GameObject skillPrefab = Resources.Load<GameObject>("Prefabs/SkillValuesInputs");

            GameObject skill = Instantiate(skillPrefab, transform);
            skill.name = ((Types)i).ToString();

            skill.GetComponent<Image>().sprite = Resources.Load<Sprite>("Sprites/UI/" + ((Types)i).ToString());

            skill.transform.GetChild(0).GetComponent<TextMeshProUGUI>().text = ((Types)i).ToString();
            skill.transform.GetChild(1).GetComponent<TextMeshProUGUI>().text = ((Types)i).ToString()[..2]; // First 2 letters

            skill.transform.position -= new Vector3(0, 18 * i, 0) * configCanvas.transform.localScale.x;

            SkillValuesInputs input = new((Types)i, skill.transform.GetChild(2).GetComponent<TMP_InputField>(), skill.transform.GetChild(3).GetComponent<TMP_InputField>());

            inputs.Add(input);
        }

        //foreach (var type in GetComponentsInChildren<Image>()) 
        //{ 
        //    if(type.name == ((Types)index).ToString())
        //    {
        //        SkillValuesInputs input = new((Types)index, type.transform.GetChild(2).GetComponent<TMP_InputField>(), type.transform.GetChild(3).GetComponent<TMP_InputField>());
        //        inputs.Add(input);
        //        index++;
        //    }
        //}

        foreach(var input in inputs)
        {
            input.spDef.onEndEdit.AddListener(delegate { confUI.ChangeSPDefAndAtk(input.type, int.Parse(input.spDef.text), int.Parse(input.spAtk.text)); });
            input.spAtk.onEndEdit.AddListener(delegate { confUI.ChangeSPDefAndAtk(input.type, int.Parse(input.spDef.text), int.Parse(input.spAtk.text)); });
        }

        // Randomize values
        RandomButton.GetComponent<Button>().onClick.AddListener(delegate { confUI.RandomizeValues(); });

        RandomButton.transform.position = transform.position - new Vector3(67.24997f, 18 * inputs.Count + 2, 0) * configCanvas.transform.localScale.x;

        // Paste values
        PasteButton.GetComponent<Button>().onClick.AddListener(delegate { PasteValues(); });

        configCanvas.GetComponent<Canvas>().enabled = false;
    }

    void PasteValues()
    {
        if (GameObject.Find("SkillsValues") == null) return;

        string clipboardText = GUIUtility.systemCopyBuffer;
        if (clipboardText.Length > 0)
        {
            Debug.Log(clipboardText);
            string[] values = clipboardText.Split('\n');

            var types = System.Enum.GetValues(typeof(Types));

            if (values[0].Split("\t").Length < 2)
            {
                Debug.Log("Not enough values");
                return;
            }

            if (values[0].Split("\t").Length > 2)
            {
                Debug.Log("Too many values");
                return;
            }

            for (int i = 0; i < values.Length - 1; i++)
            {
                if(i >= types.Length) break;

                string[] stats = values[i].Split('\t');

                confUI.ChangeSPDefAndAtk((Types)i, int.Parse(stats[0]), int.Parse(stats[1]));
            }
        }
    }

    public void SetValues(BattleCharacter character, CharConfUI confUI)
    {
        this.confUI = confUI;

        foreach(var input in inputs)
        {
            input.spDef.text = character.skills.GetValues(input.type).spDef.ToString();
            input.spAtk.text = character.skills.GetValues(input.type).spAtk.ToString();
        }
    }
}


public class SkillValuesInputs
{
    public TMP_InputField spDef, spAtk;
    public Types type;

    public SkillValuesInputs(Types type, TMP_InputField spDef, TMP_InputField spAtk)
    {
        this.type = type;
        this.spDef = spDef;
        this.spAtk = spAtk;
    }
}
