using UnityEngine;
using System.Collections;

public class TileCollisionHandler : MonoBehaviour
{
    private Tile tile;

    GameObject DraggedTile = null;
    
    GridCreator gridCreator;

    // distance between the cursor and the tile's center, used to switch the tile's possitions with the cursor,
    // if this value is higher 0.25f, it conflits with the the other collision trigger, because the distance
    // when they switch possitions can make it enter the trigger, switching again
    readonly float radius = 0.25f;
    //bool isSwappimg = false;
    //bool ignoreTriggerEvents = false;

    void Start()
    {
        gridCreator = transform.parent.GetComponent<GridCreator>();
    }

    private void Update()
    {

        if (tile.isDragged && transform.GetChild(0).localPosition != new Vector3(0, 0.3f)) transform.GetChild(0).localPosition = new(0, 0.3f, -1f);
        else if (!tile.isDragged && transform.GetChild(0).localPosition != Vector3.zero) transform.GetChild(0).localPosition = Vector3.zero;

        if (GameObject.Find("Main Camera").GetComponent<Camera>().enabled && Input.touchCount > 0) // checks if the main camera is enabled and there is a touch on the screen
        {
            Vector3 cursorPos = Camera.main.ScreenToWorldPoint(Input.GetTouch(0).position);

            // calculates the distance between the cursor and the tile
            float distance = Vector3.Distance(transform.position, cursorPos - new Vector3(0, 0f, cursorPos.z - transform.position.z));

            // checks if the distance is less than the radius
            if (distance <= radius && DraggedTile != null && !tile.isDragged /*&& !isSwappimg*/)
            {
                (gridCreator.tempPos, tile.tile.transform.position) = (tile.tile.transform.position, gridCreator.tempPos);
                //StartCoroutine(SwapTiles(gridCreator, tile, 0.15f));
                //isSwappimg = false;

                gridCreator.UpdateTempTiles(DraggedTile, tile.tile);
            }
        }
    }

    public void Initialize(Tile tile) // gets the tile, and saves it, need so it can obtain the values of the tile that its supposed to be
    {
        this.tile = tile;
    }

    void OnTriggerEnter2D(Collider2D col) // switches the tiles if the one that enter the trigger is dragged
    {

        //if (ignoreTriggerEvents)
        //{
        //    Debug.Log("Ignoring OnTriggerEnter2D call with ignoreTriggerEvents set to true");
        //    return;
        //}

        //Debug.Log("OnTriggerEnter2D called by collider: " + col.name);


        //if (isSwappimg && col.gameObject == DraggedTile)
        //{
        //    Debug.Log("Ignoring OnTriggerEnter2D call while swap is in progress");
        //    return;
        //}

        //if (isSwappimg)
        //{
        //    Debug.Log("Swap already in progress, exiting");
        //    return;
        //}

        DraggedTile = col.gameObject; // saves the dragged tile GameObject
        //Debug.Log("Dragged tile: " + DraggedTile.name);

        int[] indexes = GridCreator.GetIndexes(col.gameObject); // gets its indexes inside the Tile array

        if (!tile.isDragged && GridCreator.IsDragged(indexes) /*&& !isSwappimg*/) // checks if it should switch them
        {
            //// Disable the swapped tile's collider
            //tile.tile.GetComponent<CircleCollider2D>().enabled = false;
            //bool CircleColliderEnabled = tile.tile.GetComponent<CircleCollider2D>().enabled;
            //Debug.Log("Disabling collider of tile: " + tile.tile.name + " " + CircleColliderEnabled);

            (gridCreator.tempPos, tile.tile.transform.position) = (tile.tile.transform.position, gridCreator.tempPos);
            //Debug.Log("Starting swap animation");
            //Debug.Log("Swapping tile: " + tile.tile.name);
            //StartCoroutine(SwapTiles(gridCreator, tile, 0.1f));
            //isSwappimg = false;

            gridCreator.UpdateTempTiles(col.gameObject, tile.tile);
        }
    }

    void OnTriggerExit2D()
    {
        DraggedTile = null; // resets the dragged tile GameObject
        //isSwappimg = false;
    }

    IEnumerator SwapTiles(GridCreator gridCreator, Tile tile, float duration)
    {
        //ignoreTriggerEvents = true;

        //isSwappimg = true;
        Debug.Log("SwapTiles coroutine started");

        // Disable the collider of the tile that is being swapped
        tile.tile.GetComponent<CircleCollider2D>().enabled = false;
        bool CircleColliderEnabled = tile.tile.GetComponent<CircleCollider2D>().enabled;
        Debug.Log("Disabling collider of tile: " + tile.tile.name + " " + CircleColliderEnabled);

        //Vector3 temp = gridCreator.tempPos;
        //gridCreator.tempPos = tile.tile.transform.position;
        //tile.tile.transform.position = temp;

        Vector3 initialPos1 = gridCreator.tempPos;
        Vector3 initialPos2 = tile.tile.transform.position;
        Vector3 midPoint = (initialPos1 + initialPos2) / 2;

        float elapsedTime = 0;
        while (elapsedTime < duration)
        {
            float t = elapsedTime / duration;
            float angle = Mathf.PI * t; // half-circle angle

            Vector3 offset1 = new Vector3(Mathf.Cos(angle), Mathf.Sin(angle), 0) * (initialPos2 - midPoint).magnitude;
            Vector3 offset2 = new Vector3(Mathf.Cos(angle), -Mathf.Sin(angle), 0) * (initialPos2 - midPoint).magnitude;

            gridCreator.tempPos = midPoint + offset1;
            tile.tile.transform.position = midPoint + offset2;

            elapsedTime += Time.deltaTime;
            yield return null;
        }

        gridCreator.tempPos = initialPos2;
        tile.tile.transform.position = initialPos1;

        // Enable the collider again after the swap animation is finished
        tile.tile.GetComponent<CircleCollider2D>().enabled = true;
        Debug.Log("Disabling collider of tile: " + tile.tile.name + " " + CircleColliderEnabled);

        Debug.Log("SwapTiles coroutine finished");
        //isSwappimg = false;

        //ignoreTriggerEvents = false;
    }

}
