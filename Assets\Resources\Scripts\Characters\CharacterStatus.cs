using System.Collections.Generic;
using System.Linq;
using UnityEngine;

public class CharacterStatus
{
    List<int> apMin, hp, atk, def, atkLim; // list of the five status on a character

    public List<int> bl; // the list that contains the level for each status

    public CharacterStatus()
    {
        apMin = new();
        hp = new();
        atk = new();
        def = new();
        atkLim = new();

        bl = Enumerable.Range(0, 101).ToList(); // creates a list from 0 to 100
        foreach (var i in bl)
        {
            apMin.Add(i / 10); // sets the apMin for each level
            hp.Add(Random.Range(50, 101) * (i + 1)); // sets the hp to random value between 50 and 100 times the level
            atk.Add(Random.Range(50, 101) * (i + 1)); // sets the atk to random value between 50 and 100 times the level
            def.Add(Random.Range(50, 101) * (i + 1)); // sets the def to random value between 50 and 100 times the level
            atkLim.Add(atk[i] * 2); // sets the atkLim to double the atk
        }
    }

    public List<int> GetApMin() => apMin; // gets the list of apMin

    public List<int> GetBl() => bl; // gets the list of the levels


    public int GetHp(int index) => hp[index]; // gets the hp for a specific level
    public List<int> GetHp() => hp; // gets the list of hp


    public int GetAtk(int index) => atk[index]; // gets the atk for a specific level
    public List<int> GetAtk() => atk; // gets the list of atk


    public int GetDef(int index) => def[index]; // gets the def for a specific level
    public List<int> GetDef() => def; // gets the list of def


    public int GetAtkLim(int index) => atkLim[index]; // gets the atkLim for a specific level
    public List<int> GetAtkLim() => atkLim; // gets the list of atkLim


    public void UpdateApMin(List<int> newApMin) => apMin = newApMin; // updates the list of apMin
    public void UpdateBl() => bl = Enumerable.Range(0, apMin.Count).ToList(); // updates the list of the levels using the list of apMin going from 0 to the amout of values in apMin minus one
    public void UpdateHp(List<int> newHp) => hp = newHp; // updates the list of hp
    public void UpdateAtk(List<int> newAtk) => atk = newAtk; // updates the list of atk
    public void UpdateDef(List<int> newDef) => def = newDef; // updates the list of def
    public void UpdateAtkLim(List<int> newAtkLim) => atkLim = newAtkLim; // updates the list of atkLim
}
