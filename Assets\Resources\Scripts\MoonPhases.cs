// Moon Phases handler

using System.Globalization;
using UnityEngine;

public enum MoonPhases
{
    Nova,
    Crescente,
    QuartoCrescente,
    GibosaCrescente,
    LuaCheia,
    GibosaMinguante,
    QuartoMinguante,
    Minguante
}

public class MoonPhaseData
{
    public string id;
    public float partyDamageMultiplier;
    public float enemyDamageMultiplier;

    public MoonPhaseData(string id)
    {
        this.id = id;
        string partyDamageinput = GeneralInfo.GetMoonPhaseDamageParty(id)
                .Replace(',', '.'); // Normalize if needed
        partyDamageMultiplier = float.Parse(partyDamageinput, CultureInfo.InvariantCulture);
        string enemyDamageinput = GeneralInfo.GetMoonPhaseDamageOpponent(id)
                .Replace(',', '.'); // Normalize if needed
        enemyDamageMultiplier = float.Parse(enemyDamageinput, CultureInfo.InvariantCulture);
    }
}
