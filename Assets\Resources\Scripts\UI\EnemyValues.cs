using UnityEngine;
using TMPro;
using UnityEngine.UI;
using System.Linq;
using System.Collections;
using UnityEngine.EventSystems;
using DG.Tweening;
using System;

public class EnemyValues : <PERSON>o<PERSON><PERSON><PERSON>our, IBegin<PERSON><PERSON><PERSON><PERSON><PERSON>, IEndDragHandler
{
    BattleChara<PERSON> character = null; // the enemy character to show the values

    ScrollRect scrollRect;

    ConfigsHandler configsHandler;

    // References to the UI elements
    public GameObject level, healthBar, strength, magic, fire, venom, possession, electricity, acid, frost, charName, rarity,
                      charm, confusion, curse, paralysis, sleep,
                      modsLabel, knoLabel, lckLabel, spdLabel, evsLabel, prcLabel, critLabel, pryLabel, skillsLabel, descriptionLabel, birthYear, height, gender, charClass, archetype;

    Button strengthSpDefIconBtn, magicSpDefIconBtn, fireSpDefIconBtn, venomSpDefIconBtn, possessionSpDefIconBtn, electricitySpDefIconBtn, acidSpDefIconBtn, frostSpDefIconBtn,
        charmButton, confusionButton, curseButton, paralysisButton, sleepButton,
        qiButton, luckButton, speedButton, precisionButton, evasionButton, criticalButton, parryButton,
        charClassButton, archetypeButton, genderButton, birthYearButton, heightButton;

    public GameObject pageIndicator1;
    public GameObject pageIndicator2;
    public GameObject pageIndicator3;

    Image healthBarImg;
    TextMeshProUGUI healthAmount;

    float dragThreshold = 0.001f;
    private float dragStartPos;

    int year = DateTime.Now.Year;

    private void Start()
    {
        configsHandler = GameObject.Find("GameConfigsHandler").GetComponent<ConfigsHandler>();

        scrollRect = gameObject.GetComponent<ScrollRect>();
        scrollRect.onValueChanged.AddListener(_ =>
        {
            HandlePage(); // Updates the page indicator based on the scroll position
        });

        pageIndicator1.GetComponent<Button>().onClick.AddListener(() => SnapTo(0f));
        pageIndicator2.GetComponent<Button>().onClick.AddListener(() => SnapTo(0.5f));
        pageIndicator3.GetComponent<Button>().onClick.AddListener(() => SnapTo(1f));



        pageIndicator1.GetComponent<Image>().color = Color.white;
        pageIndicator2.GetComponent<Image>().color = new Color(1f, 1f, 1f, 0.3f);
        pageIndicator3.GetComponent<Image>().color = new Color(1f, 1f, 1f, 0.3f);

        healthBarImg = healthBar.transform.GetChild(0).GetComponent<Image>();
        healthAmount = healthBar.transform.GetChild(1).GetChild(0).GetComponent<TextMeshProUGUI>();

        skillsLabel.GetComponent<TextMeshProUGUI>().text = KeywordManager.GetWord("COLLECTIBLE_SKILLS");
        modsLabel.GetComponent<TextMeshProUGUI>().text = KeywordManager.GetWord("COLLECTIBLE_MODIFIERS");

        knoLabel.GetComponent<TextMeshProUGUI>().text = GeneralInfo.GetKnowledgeAcronym();
        lckLabel.GetComponent<TextMeshProUGUI>().text = GeneralInfo.GetLuckAcronym();
        spdLabel.GetComponent<TextMeshProUGUI>().text = GeneralInfo.GetSpeedAcronym();
        evsLabel.GetComponent<TextMeshProUGUI>().text = GeneralInfo.GetEvasionAcronym();
        prcLabel.GetComponent<TextMeshProUGUI>().text = GeneralInfo.GetPrecisionAcronym();
        critLabel.GetComponent<TextMeshProUGUI>().text = GeneralInfo.GetCriticalChanceAcronym();
        pryLabel.GetComponent<TextMeshProUGUI>().text = GeneralInfo.GetParryChanceAcronym();

        // Get the buttons
        strengthSpDefIconBtn = strength.transform.GetChild(1).GetChild(0).GetComponent<Button>();
        magicSpDefIconBtn = magic.transform.GetChild(1).GetChild(0).GetComponent<Button>();
        fireSpDefIconBtn = fire.transform.GetChild(1).GetChild(0).GetComponent<Button>();
        venomSpDefIconBtn = venom.transform.GetChild(1).GetChild(0).GetComponent<Button>();
        possessionSpDefIconBtn = possession.transform.GetChild(1).GetChild(0).GetComponent<Button>();
        electricitySpDefIconBtn = electricity.transform.GetChild(1).GetChild(0).GetComponent<Button>();
        acidSpDefIconBtn = acid.transform.GetChild(1).GetChild(0).GetComponent<Button>();
        frostSpDefIconBtn = frost.transform.GetChild(1).GetChild(0).GetComponent<Button>();

        charmButton = charm.transform.GetChild(2).GetComponent<Button>();
        confusionButton = confusion.transform.GetChild(2).GetComponent<Button>();
        curseButton = curse.transform.GetChild(2).GetComponent<Button>();
        paralysisButton = paralysis.transform.GetChild(2).GetComponent<Button>();
        sleepButton = sleep.transform.GetChild(2).GetComponent<Button>();
        qiButton = knoLabel.GetComponent<Button>();
        luckButton = lckLabel.GetComponent<Button>();
        speedButton = spdLabel.GetComponent<Button>();
        precisionButton = prcLabel.GetComponent<Button>();
        evasionButton = evsLabel.GetComponent<Button>();
        criticalButton = critLabel.GetComponent<Button>();
        parryButton = pryLabel.GetComponent<Button>();
        charClassButton = charClass.GetComponent<Button>();
        archetypeButton = archetype.GetComponent<Button>();
        birthYearButton = birthYear.GetComponent<Button>();
        heightButton = height.GetComponent<Button>();
        genderButton = gender.GetComponent<Button>();

        // Add the listeners to the buttons
        strengthSpDefIconBtn.onClick.AddListener(() => InfoBox.Instance.DisplayAilmentOrSkill(false, null, character.skills.GetValues(Types.Strength).spDef.ToString()));
        magicSpDefIconBtn.onClick.AddListener(() => InfoBox.Instance.DisplayAilmentOrSkill(false, null, character.skills.GetValues(Types.Magic).spDef.ToString()));
        fireSpDefIconBtn.onClick.AddListener(() => InfoBox.Instance.DisplayAilmentOrSkill(false, null, character.skills.GetValues(Types.Fire).spDef.ToString()));
        venomSpDefIconBtn.onClick.AddListener(() => InfoBox.Instance.DisplayAilmentOrSkill(false, null, character.skills.GetValues(Types.Venom).spDef.ToString()));
        possessionSpDefIconBtn.onClick.AddListener(() => InfoBox.Instance.DisplayAilmentOrSkill(false, null, character.skills.GetValues(Types.Possession).spDef.ToString()));
        electricitySpDefIconBtn.onClick.AddListener(() => InfoBox.Instance.DisplayAilmentOrSkill(false, null, character.skills.GetValues(Types.Electricity).spDef.ToString()));
        acidSpDefIconBtn.onClick.AddListener(() => InfoBox.Instance.DisplayAilmentOrSkill(false, null, character.skills.GetValues(Types.Acid).spDef.ToString()));
        frostSpDefIconBtn.onClick.AddListener(() => InfoBox.Instance.DisplayAilmentOrSkill(false, null, character.skills.GetValues(Types.Frost).spDef.ToString()));

        //(charm, character.ailDefs.GetCharm());
        charmButton.onClick.AddListener(() => InfoBox.Instance.DisplayAilmentOrSkill(true, GeneralInfo.GetCharmAilmentName(), character.ailDefs.GetCharm()));
        confusionButton.onClick.AddListener(() => InfoBox.Instance.DisplayAilmentOrSkill(true, GeneralInfo.GetConfusionAilmentName(), character.ailDefs.GetConfusion()));
        curseButton.onClick.AddListener(() => InfoBox.Instance.DisplayAilmentOrSkill(true, GeneralInfo.GetCurseAilmentName(), character.ailDefs.GetCurse()));
        paralysisButton.onClick.AddListener(() => InfoBox.Instance.DisplayAilmentOrSkill(true, GeneralInfo.GetParalysisAilmentName(), character.ailDefs.GetParalysis()));
        sleepButton.onClick.AddListener(() => InfoBox.Instance.DisplayAilmentOrSkill(true, GeneralInfo.GetSleepAilmentName(), character.ailDefs.GetSleep()));

        qiButton.onClick.AddListener(() => InfoBox.Instance.DisplayModifier(GeneralInfo.GetKnowledgeSkillName(), GeneralInfo.GetKnowledgeDescription()));
        luckButton.onClick.AddListener(() => InfoBox.Instance.DisplayModifier(GeneralInfo.GetLuckSkillName(), GeneralInfo.GetLuckDescription()));
        speedButton.onClick.AddListener(() => InfoBox.Instance.DisplayModifier(GeneralInfo.GetSpeedSkillName(), GeneralInfo.GetSpeedDescription()));
        precisionButton.onClick.AddListener(() => InfoBox.Instance.DisplayModifier(GeneralInfo.GetPrecisionSkillName(), GeneralInfo.GetPrecisionDescription()));
        evasionButton.onClick.AddListener(() => InfoBox.Instance.DisplayModifier(GeneralInfo.GetEvasionSkillName(), GeneralInfo.GetEvasionDescription()));
        criticalButton.onClick.AddListener(() => InfoBox.Instance.DisplayModifier(GeneralInfo.GetCriticalChanceSkillName(), GeneralInfo.GetCriticalChanceDescription()));
        parryButton.onClick.AddListener(() => InfoBox.Instance.DisplayModifier(GeneralInfo.GetParryChanceSkillName(), GeneralInfo.GetParryChanceDescription()));

        charClassButton.onClick.AddListener(() => InfoBox.Instance.DisplayClassORArchetype(true, character.classe, GeneralInfo.GetClassDescriptionByName(character.classe)));
        archetypeButton.onClick.AddListener(() => InfoBox.Instance.DisplayClassORArchetype(false, GeneralInfo.GetClassArchetypeByName(character.classe), GeneralInfo.GetArchetypeDescriptionByName(GeneralInfo.GetClassArchetypeByName(character.classe))));
        birthYearButton.onClick.AddListener(() => InfoBox.Instance.DisplayAgeOrHeight(true));
        heightButton.onClick.AddListener(() => InfoBox.Instance.DisplayAgeOrHeight(false));
        genderButton.onClick.AddListener(() => InfoBox.Instance.DisplayGender(character.gender));
    }

    void Update()
    {

        if (character != null) // if the enemy isn't null it updates the values for the values in the enemy
        {
            level.GetComponent<TextMeshProUGUI>().text = character.level.ToString();
            charName.GetComponent<TextMeshProUGUI>().text = character.name;
            rarity.GetComponent<Image>().color = GeneralInfo.GetTierColorByName(character.rarity);
            rarity.transform.GetChild(0).GetComponent<TextMeshProUGUI>().text = character.rarity;
            LayoutRebuilder.ForceRebuildLayoutImmediate(rarity.GetComponent<RectTransform>());

            healthBarImg.fillAmount = character.hP / (float)character.maxHP;

            healthBarImg.color =
                Tools.InterpolateColor(
                    new(178f / 255f, 250f / 255f, 67f / 255f),
                    Color.red,
                    healthBarImg.fillAmount,
                    5f);

            healthAmount.text = !character.isEnemy || (character.isEnemy && configsHandler.ShowHP.GetComponent<Toggle>().isOn) ? character.hP.ToString() : "???";

            UpdateSkillDisplay(strength, character.skills.GetValues(Types.Strength), Types.Strength);
            UpdateSkillDisplay(magic, character.skills.GetValues(Types.Magic), Types.Magic);
            UpdateSkillDisplay(fire, character.skills.GetValues(Types.Fire), Types.Fire);
            UpdateSkillDisplay(venom, character.skills.GetValues(Types.Venom), Types.Venom);
            UpdateSkillDisplay(possession, character.skills.GetValues(Types.Possession), Types.Possession);
            UpdateSkillDisplay(electricity, character.skills.GetValues(Types.Electricity), Types.Electricity);
            UpdateSkillDisplay(acid, character.skills.GetValues(Types.Acid), Types.Acid);
            UpdateSkillDisplay(frost, character.skills.GetValues(Types.Frost), Types.Frost);


            UpdateAilmentUI(charm, character.ailDefs.GetCharm());
            UpdateAilmentUI(confusion, character.ailDefs.GetConfusion());
            UpdateAilmentUI(curse, character.ailDefs.GetCurse());
            UpdateAilmentUI(paralysis, character.ailDefs.GetParalysis());
            UpdateAilmentUI(sleep, character.ailDefs.GetSleep());

            UpdateMods();
            UpdateDescription();
        }
    }

    public void OnBeginDrag(PointerEventData eventData)
    {
        // Record starting scroll position (normalized between 0 and 1)
        dragStartPos = scrollRect.horizontalNormalizedPosition;
    }

    public void OnEndDrag(PointerEventData eventData)
    {
        float currentPos = scrollRect.horizontalNormalizedPosition;
        float dragDistance = Mathf.Abs(currentPos - dragStartPos);

        // Define snap points
        float[] snapPoints = { 0f, 0.5f, 1f };

        if (dragDistance < dragThreshold)
        {
            // Not enough movement: snap to the nearest page
            SnapTo(GetClosestSnapPoint(currentPos, snapPoints));
        }
        else
        {
            // Determine drag direction
            bool draggingRight = currentPos > dragStartPos;

            // Find current page index
            int startIndex = GetClosestSnapPointIndex(dragStartPos, snapPoints);
            int targetIndex = Mathf.Clamp(startIndex + (draggingRight ? 1 : -1), 0, snapPoints.Length - 1);

            SnapTo(snapPoints[targetIndex]);
        }
    }

    private float GetClosestSnapPoint(float pos, float[] snapPoints)
    {
        float closest = snapPoints[0];
        float minDist = Mathf.Abs(pos - closest);

        for (int i = 1; i < snapPoints.Length; i++)
        {
            float dist = Mathf.Abs(pos - snapPoints[i]);
            if (dist < minDist)
            {
                minDist = dist;
                closest = snapPoints[i];
            }
        }

        return closest;
    }

    private int GetClosestSnapPointIndex(float pos, float[] snapPoints)
    {
        int closestIndex = 0;
        float minDist = Mathf.Abs(pos - snapPoints[0]);

        for (int i = 1; i < snapPoints.Length; i++)
        {
            float dist = Mathf.Abs(pos - snapPoints[i]);
            if (dist < minDist)
            {
                minDist = dist;
                closestIndex = i;
            }
        }

        return closestIndex;
    }

    public void SnapTo(float target)
    {
        // Stop any previous tweens on the scrollRect
        DOTween.Kill(scrollRect);

        // Tween to the target horizontalNormalizedPosition
        scrollRect.DOHorizontalNormalizedPos(target, 0.3f)
            .SetEase(Ease.OutCubic);
    }

    void UpdateSkillDisplay(GameObject skillObj, SkillValues skill, Types type)
    {
        // Cache references
        var defText = skillObj.transform.GetChild(0).GetComponent<TextMeshProUGUI>();
        var defIcon = skillObj.transform.GetChild(1).GetComponent<Image>();
        var atkText = skillObj.transform.GetChild(2).GetComponent<TextMeshProUGUI>();

        bool isEnemy = character.isEnemy;
        //bool enemyHasUsed = true; // Set to false if needed (based on your comment logic)
        bool playerHasUsed = configsHandler.playerAttacksType[type] != 0;

        // Hide info if enemy and player haven't used the type yet
        if (isEnemy && !playerHasUsed /* && !enemyHasUsed */)
        {
            defText.gameObject.SetActive(true);
            defIcon.gameObject.SetActive(false);
            defText.text = "?";
            atkText.text = "?";
            return;
        }

        string atkValue = skill.spAtk?.ToString();
        string defValue = skill.spDef?.ToString();

        bool isSpecialDef = defValue == "Nulo" || defValue == "Repelir" || defValue == "Absorver";

        // Handle special defense icons
        if (isSpecialDef)
        {
            defText.gameObject.SetActive(false);
            defIcon.gameObject.SetActive(true);
            defIcon.sprite = Resources.Load<Sprite>("Sprites/UI/" + defValue);
        }
        else
        {
            defText.gameObject.SetActive(true);
            defIcon.gameObject.SetActive(false);

            // Format 0 or null as ━
            string defDisplay = string.IsNullOrEmpty(defValue) || defValue == "0" ? "━" : defValue;

            // Set color
            if (int.TryParse(defValue, out int def) && def < 0)
                defText.text = $"<color=red>{defDisplay}</color>";
            else
                defText.text = $"<color=white>{defDisplay}</color>";
        }

        // Format attack value
        string atkDisplay = string.IsNullOrEmpty(atkValue) || atkValue == "0" ? "━" : atkValue;

        if (int.TryParse(atkValue, out int atk) && atk < 0)
            atkText.text = $"<color=red>{atkDisplay}</color>";
        else
            atkText.text = $"<color=white>{atkDisplay}</color>";
    }

    // Updates the character to show the values
    public void UpdateCharacter(BattleCharacter character) => this.character = character;

    void HandlePage()
    {
        if (scrollRect.horizontalNormalizedPosition < 0.5f)
        {
            pageIndicator1.GetComponent<Image>().color = Color.white;
            pageIndicator2.GetComponent<Image>().color = new Color(1f, 1f, 1f, 0.3f);
            pageIndicator3.GetComponent<Image>().color = new Color(1f, 1f, 1f, 0.3f);
        }
        else if (Mathf.Abs(scrollRect.horizontalNormalizedPosition - 0.5f) < 0.1f) // Check if the position is close enough to the middle
        {
            pageIndicator1.GetComponent<Image>().color = new Color(1f, 1f, 1f, 0.3f);
            pageIndicator2.GetComponent<Image>().color = Color.white;
            pageIndicator3.GetComponent<Image>().color = new Color(1f, 1f, 1f, 0.3f);
        }
        else
        {
            pageIndicator1.GetComponent<Image>().color = new Color(1f, 1f, 1f, 0.3f);
            pageIndicator2.GetComponent<Image>().color = new Color(1f, 1f, 1f, 0.3f);
            pageIndicator3.GetComponent<Image>().color = Color.white;
        }
    }

    void UpdateAilmentUI(GameObject ailment, string value)
    {
        GameObject iconA = ailment.transform.GetChild(0).gameObject;
        GameObject iconB = ailment.transform.GetChild(1).gameObject;

        string spritePath = value switch
        {
            "Fraco" => "Sprites/UI/Fraco",
            "Resiste" => "Sprites/UI/Resiste",
            "Imune" => "Sprites/UI/Nulo",
            "Repele" => "Sprites/UI/Repelir",
            _ => null
        };

        if (value == "Normal" || spritePath == null)
        {
            iconA.SetActive(false);
            iconB.SetActive(true);
        }
        else
        {
            iconB.SetActive(false);
            iconA.SetActive(true);
            Image imageComponent = iconA.GetComponent<Image>();
            imageComponent.sprite = Resources.Load<Sprite>(spritePath);
        }
    }

    void UpdateMods()
    {
        knoLabel.transform.GetChild(0).GetComponent<TextMeshProUGUI>().text = character.mods.GetKnowledge().ToString();
        lckLabel.transform.GetChild(0).GetComponent<TextMeshProUGUI>().text = character.mods.GetLuck().ToString();
        spdLabel.transform.GetChild(0).GetComponent<TextMeshProUGUI>().text = character.mods.GetSpeed().ToString();
        evsLabel.transform.GetChild(0).GetComponent<TextMeshProUGUI>().text = character.mods.GetEvasion().ToString() + "%";
        prcLabel.transform.GetChild(0).GetComponent<TextMeshProUGUI>().text = character.mods.GetPrecision().ToString() + "%";
        critLabel.transform.GetChild(0).GetComponent<TextMeshProUGUI>().text = character.mods.GetCriticalChance().ToString() + "%";
        pryLabel.transform.GetChild(0).GetComponent<TextMeshProUGUI>().text = character.mods.GetParryChance().ToString() + "%";
    }

    void UpdateDescription()
    {
        descriptionLabel.transform.GetChild(0).GetComponent<TextMeshProUGUI>().text = character.title;
        descriptionLabel.transform.GetChild(1).GetComponent<TextMeshProUGUI>().text = character.description;
        if (character.birthyear == "?" || character.birthyear == "") { birthYear.transform.GetChild(1).GetComponent<TextMeshProUGUI>().text = "?"; }
        else
        {
            int age = year - int.Parse(character.birthyear);
            birthYear.transform.GetChild(1).GetComponent<TextMeshProUGUI>().text = age.ToString();
        }
        height.transform.GetChild(1).GetComponent<TextMeshProUGUI>().text = character.height + "m";
        string charGender = character.gender;
        switch (charGender)
        {
            case "1":
                gender.transform.GetChild(0).gameObject.SetActive(false);
                gender.transform.GetChild(1).gameObject.SetActive(true);
                gender.transform.GetChild(2).gameObject.SetActive(false);
                gender.transform.GetChild(3).GetComponent<TextMeshProUGUI>().text = KeywordManager.GetWord("SEX_FEMALE");
                break;
            case "2":
                gender.transform.GetChild(0).gameObject.SetActive(true);
                gender.transform.GetChild(1).gameObject.SetActive(false);
                gender.transform.GetChild(2).gameObject.SetActive(false);
                gender.transform.GetChild(3).GetComponent<TextMeshProUGUI>().text = KeywordManager.GetWord("SEX_MALE");
                break;
            default:
                gender.transform.GetChild(0).gameObject.SetActive(false);
                gender.transform.GetChild(1).gameObject.SetActive(false);
                gender.transform.GetChild(2).gameObject.SetActive(true);
                gender.transform.GetChild(3).GetComponent<TextMeshProUGUI>().text = KeywordManager.GetWord("SEX_UNDEFINED");
                break;
        }

        charClass.transform.GetChild(1).GetComponent<TextMeshProUGUI>().text = character.classe;
        archetype.transform.GetChild(1).GetComponent<TextMeshProUGUI>().text = GeneralInfo.GetClassArchetypeByName(character.classe);

    }
}
