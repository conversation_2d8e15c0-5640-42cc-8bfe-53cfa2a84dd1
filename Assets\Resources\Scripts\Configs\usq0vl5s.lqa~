using System.Collections.Generic;
using TMPro;
using UnityEngine;
using UnityEngine.UI;
using System;
using System.Linq;

public class StatsValueDisplay : MonoBehaviour
{
    public GameObject apmin, bl, hp, atk, def, atkLim;

    float scrollSpeed = 5f;

    float pos = 0.45f;

    CharConfUI confUI;

    Button pasteButton;

    private void Start()
    {
        pasteButton = GameObject.Find("PasteButton").GetComponent<Button>();

        pasteButton.onClick.AddListener(PasteValues);
    }

    private void Update()
    {
        Camera confCamera = GameObject.Find("Configs Camera").GetComponent<Camera>();

        if (confCamera != null && confCamera.enabled)
        {

            if (Input.mousePosition.x / confCamera.pixelWidth > pos)
            {
                Vector3 movement = new Vector3(0f, Input.GetAxis("Mouse ScrollWheel") * scrollSpeed, 0f);

                transform.Translate(movement);
            }
        }
    }

    void PasteValues()
    {
        string clipboardText = GUIUtility.systemCopyBuffer;
        if(clipboardText.Length > 0) 
        {
            int s = 0; // has the six values or not
            Debug.Log(clipboardText); 
            string[] values = clipboardText.Split('\n');

            List<int> apmin = new();
            List<int> bl = new();
            List<int> hp = new();
            List<int> atk = new();
            List<int> def = new();
            List<int> atkLim = new();

            if (values[0].Split("\t").Length < 6) s = 1;

            if(values[0].Split("\t").Length < 5) 
            { 
                Debug.Log("Not enough values");
                return;
            }

            for(int i = 0; i < values.Length - 1; i++)
            { 
                string[] stats = values[i].Split('\t');

                apmin.Add((int)Math.Floor((decimal)(i / 10)));
                bl.Add((s == 1) ? i : int.Parse(stats[1]));
                hp.Add(int.Parse(stats[2 - s]));
                atk.Add(int.Parse(stats[3 - s]));
                def.Add(int.Parse(stats[4 - s]));
                atkLim.Add(int.Parse(stats[5 - s]));
            }

            Debug.Log(string.Join("", apmin));
            Debug.Log(string.Join("", bl));
            Debug.Log(string.Join("", hp));
            Debug.Log(string.Join("", atk));
            Debug.Log(string.Join("", def));
            Debug.Log(string.Join("", atkLim));
        }
    }


    public void SetValues(BattleCharacter character, CharConfUI confUI)
    {
        this.confUI = confUI;

        apmin.GetComponent<TextMeshProUGUI>().text = "Apmin\n" + string.Join("\n", character.stats.GetApMin());
        bl.GetComponent<TextMeshProUGUI>().text = "BL\n" + string.Join("\n", character.stats.GetBl());
        hp.GetComponent<TextMeshProUGUI>().text = "HP\n" + string.Join("\n", character.stats.GetHp());
        atk.GetComponent<TextMeshProUGUI>().text = "Atk\n" + string.Join("\n", character.stats.GetAtk());
        def.GetComponent<TextMeshProUGUI>().text = "Def\n" + string.Join("\n", character.stats.GetDef());
        atkLim.GetComponent<TextMeshProUGUI>().text = "AtkLim\n" + string.Join("\n", character.stats.GetAtkLim());
    }
}
