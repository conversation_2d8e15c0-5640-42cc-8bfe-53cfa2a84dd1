using UnityEngine;

public class WeaponUpgrade
{
    public string id; // Unique identifier
    public string itemId;  // Item id - id of the weapon has an item
    public int level; // Level
    public int atkw; // AtkW
    public int cooldown; // Cooldown
    public int gold; // Gold
    public int ichor; // Ichor
    public int souls; // Souls
    public int time; // Time
    public int rubies; // Rubies
    public int titanium; // Titanium
    public int adamantium; // Adamantium
    public string rarityName; // Rarity name
    public int shots; // Shots
    public int spdBoost; // Speed boost
    public int goldUp; // Gold upgrade
    public int hellnium; // Hellnium

    public WeaponUpgrade(string id, string itemId, int level, int atkw, int cooldown, int gold, int ichor, int souls, int time, int rubies, int titanium, int adamantium, string rarityName, int shots, int spdBoost, int goldUp, int hellnium)
    {
        this.id = id;
        this.itemId = itemId;
        this.level = level;
        this.atkw = atkw;
        this.cooldown = cooldown;
        this.gold = gold;
        this.ichor = ichor;
        this.souls = souls;
        this.time = time;
        this.rubies = rubies;
        this.titanium = titanium;
        this.adamantium = adamantium;
        this.rarityName = rarityName;
        this.shots = shots;
        this.spdBoost = spdBoost;
        this.goldUp = goldUp;
        this.hellnium = hellnium;
    }

}
