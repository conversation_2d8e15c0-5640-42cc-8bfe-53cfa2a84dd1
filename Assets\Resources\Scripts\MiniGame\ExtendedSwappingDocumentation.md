# Cursor-Based Swapping System for Runes

## Overview
The rune swapping system now uses a unified cursor-based approach for both normal (inside-grid) and extended (outside-grid) swapping behavior. This provides more precise, predictable, and responsive gameplay experience by eliminating distance calculations entirely.

## How It Works

### Normal (Inside-Grid) Swapping
- **Cursor-Based Targeting**: Uses cursor position to directly determine which grid cell is being targeted
- **Adjacent-Only Constraint**: Only allows swapping with runes that are adjacent (horizontally, vertically, or diagonally) to the dragged rune's original position
- **No Distance Calculations**: Eliminates the previous 0.45f radius check and distance-based target selection
- **Precise Control**: Players can target specific adjacent runes by positioning the cursor over them

### Extended (Outside-Grid) Swapping

#### Vertical Movement Outside Grid
- **Above the grid**: When a rune is dragged above the top edge of the grid, it's treated as being positioned in the **first row (row 0)**
- **Below the grid**: When a rune is dragged below the bottom edge of the grid, it's treated as being positioned in the **last row**
- **Horizontal swapping**: While outside the grid vertically, horizontal movement of the dragged rune triggers horizontal swapping with runes in the corresponding mapped row

### Horizontal Movement Outside Grid
- **Left of the grid**: When a rune is dragged to the left of the grid's left edge, it's treated as being positioned in the **first column (column 0)**
- **Right of the grid**: When a rune is dragged to the right of the grid's right edge, it's treated as being positioned in the **last column**
- **Vertical swapping**: While outside the grid horizontally, vertical movement of the dragged rune triggers vertical swapping with runes in the corresponding mapped column

## Implementation Details

### Key Methods Added to GridManager.cs

1. **CalculateGridBounds()**: Calculates and caches the world-space boundaries of the grid
2. **IsOutsideGridAndGetMappedPosition()**: Detects when a rune is outside grid bounds and maps it to appropriate edge positions
3. **GetCursorBasedSwapTarget()**: Finds swap targets for extended swapping based on cursor position mapping
4. **GetCursorBasedAdjacentTarget()**: Finds adjacent swap targets for normal swapping using cursor position
5. **IsAdjacent()**: Validates that two grid positions are adjacent (including diagonally)
6. **GetColumnFromCursorX()**: Maps cursor X position to grid column index
7. **GetRowFromCursorY()**: Maps cursor Y position to grid row index
8. **ResetExtendedSwapState()**: Placeholder method for compatibility (no state needed with cursor-based approach)

### Modified Methods

1. **GetNearestAdjacentRune()**: Enhanced to support extended swapping when runes are outside grid boundaries
2. **FinalizeGridLayout()**: Updated to reset grid bounds calculation when layout changes

### Unified Cursor-Based Targeting System

The implementation uses a cursor-position-based approach for both normal and extended swapping:

**Normal Swapping (Inside Grid):**
- **Direct Grid Mapping**: Cursor position directly maps to grid coordinates to determine target rune
- **Adjacent Validation**: Ensures only adjacent runes (including diagonals) can be swapped
- **No Distance Calculations**: Eliminates the previous 0.45f radius check and distance-based selection
- **Precise Targeting**: Players can target specific adjacent runes by cursor positioning

**Extended Swapping (Outside Grid):**
- **Edge Projection**: Cursor position is projected onto appropriate edge row/column when outside grid
- **Boundary Detection**: Automatically detects when cursor is outside grid boundaries
- **Direct Selection**: No distance calculations or cooldowns needed
- **Predictable Behavior**: Consistent cursor-to-target mapping regardless of grid position

## Usage

The extended swapping behavior is automatically enabled and requires no additional setup. Players can:

1. Drag a rune normally within the grid (existing behavior unchanged)
2. Drag a rune outside the grid boundaries to trigger extended swapping
3. Move the dragged rune horizontally while above/below the grid to swap with runes in the mapped row
4. Move the dragged rune vertically while left/right of the grid to swap with runes in the mapped column

## Debug Information

When running in the Unity Editor, debug messages will be logged to the console showing:
- Direction the rune is outside the grid (above, below, left, right)
- Mapped coordinates for the extended swapping behavior

## Performance Considerations

- **Grid boundaries**: Calculated once and cached for performance
- **Boundary calculations**: Only performed when the grid layout changes
- **Eliminated distance calculations**: Both normal and extended swapping now use direct coordinate mapping
- **No radius checks**: Removed the 0.45f distance threshold and Vector2.Distance calculations
- **O(1) target selection**: Direct grid coordinate mapping instead of iterating through all directions
- **Reduced computational overhead**: No cooldowns, state tracking, or expensive mathematical operations
- **Consistent performance**: Same efficient approach for both inside-grid and outside-grid swapping

## Testing

To test the extended swapping behavior:

1. Start the game and enter the rune matching mini-game
2. Drag a rune above the grid and move it horizontally - it should swap with runes in the top row
3. Drag a rune below the grid and move it horizontally - it should swap with runes in the bottom row
4. Drag a rune to the left of the grid and move it vertically - it should swap with runes in the leftmost column
5. Drag a rune to the right of the grid and move it vertically - it should swap with runes in the rightmost column

## Customization

The extended swapping behavior can be customized by modifying:
- Grid boundary padding in `CalculateGridBounds()` (currently set to 0.5f)
- Distance calculations in `GetExtendedSwapTarget()`
- Debug logging behavior in `GetExtendedSwapTarget()`
