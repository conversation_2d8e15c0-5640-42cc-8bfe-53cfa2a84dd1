using TMPro;
using UnityEngine;

public class EditEnergyTime : MonoBeh<PERSON>our
{
    private ConfigsHandler configsHandler; // Reference to the ConfigsHandler
    private TMP_InputField text;

    private bool isEditting = false;

    void Start()
    {
        configsHandler = GameObject.Find("GameConfigsHandler").GetComponent<ConfigsHandler>(); // Get the ConfigsHandler script

        text = GetComponent<TMP_InputField>(); // Get the TMP_InputField component

        text.onSelect.AddListener(delegate { isEditting = true; }); // Add the listener to the TMP_InputField to edit the value

        text.onEndEdit.AddListener(EditValue); // Add the listener to the TMP_InputField to edit the value
    }

    void Update()
    {
        if(!isEditting) text.text = configsHandler.energyTimer.ToString(); // Update the text with the current energy timer if is not editting
    }

    void EditValue(string arg) // Edit the value of the energy timer
    {
        configsHandler.energyTimer = int.Parse(arg); // Set the energy timer to the new value
        configsHandler.SetEnergyTime(); // Set the energy time
        configsHandler.SaveEnergyTimer(); // Save the energy timer

        isEditting = false;
    }
}
