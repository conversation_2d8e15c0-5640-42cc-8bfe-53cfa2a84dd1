using UnityEngine;
using UnityEngine.EventSystems;

public class ClickCatcher : <PERSON>o<PERSON><PERSON><PERSON>our, IPointerClickHandler
{
    public GameObject partyConfs;
    public CharatersForPartyUIHandler cfUIH;
    public void OnPointerClick(PointerEventData eventData)
    {
        //Deselect all cards
        foreach (var card in partyConfs.GetComponentsInChildren<PlayerCard>())
        {
            card.HideOverlay();
        }

        foreach (var card in partyConfs.GetComponentsInChildren<CharacterValuesParty>())
        {
            card.HideOverlay();
            card.isSelected = false;
            cfUIH.currentlySelectedCharacter = null;
        }
    }
}
