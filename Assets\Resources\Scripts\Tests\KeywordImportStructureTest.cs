using System.IO;
using System.Linq;
using Newtonsoft.Json.Linq;
using UnityEngine;

/// <summary>
/// Test script specifically for verifying the corrected keyword import structure
/// Tests the nested keywordPkg.data format and ensures proper handling of metadata and extra fields
/// </summary>
public class KeywordImportStructureTest : MonoBehaviour
{
    /// <summary>
    /// Test the corrected import structure parsing
    /// Available as context menu item in Unity Editor
    /// </summary>
    [ContextMenu("Test Corrected Import Structure")]
    public void TestCorrectedImportStructure()
    {
        Debug.Log("🧪 [STRUCTURE_TEST] Testing Corrected Keyword Import Structure...");

        // Test 1: Verify DetermineTargetFile logic with nested structure
        TestDetermineTargetFileLogic();

        // Test 2: Verify ProcessKeywordImport logic with nested structure
        TestProcessKeywordImportLogic();

        // Test 3: Test metadata extraction
        TestMetadataExtraction();

        // Test 4: Test extra field handling
        TestExtraFieldHandling();

        // Test 5: Test modifier structure
        TestModifierStructure();

        Debug.Log("✅ [STRUCTURE_TEST] Corrected Import Structure Tests Completed!");
    }

    /// <summary>
    /// Test the DetermineTargetFile logic with the corrected nested structure
    /// </summary>
    private void TestDetermineTargetFileLogic()
    {
        Debug.Log("🔍 [STRUCTURE_TEST_1] Testing DetermineTargetFile Logic...");

        // Create test JSON with correct nested structure
        var testJson = new JObject
        {
            ["keywordPkg"] = new JObject
            {
                ["exportedDateTime"] = "31/07/2025_12:43:52",
                ["appVersion"] = "9.10.54",
                ["data"] = new JArray
                {
                    new JObject
                    {
                        ["id"] = "test001",
                        ["key"] = "TEST_KEY",
                        ["word"] = "Test Word",
                        ["keywordTags"] = new JArray { "TEST" },
                        ["revisionCounterWordAI"] = 5
                    }
                }
            }
        };

        // Test keyword data detection
        bool hasKeywordData = testJson["keywordPkg"]?["data"] is JArray keywordArray && keywordArray.Count > 0;

        LogTest("Nested keyword data detection", hasKeywordData.ToString(), "True");

        // Test that old structure would fail
        var oldStructureJson = new JObject
        {
            ["keywordPkg"] = new JArray
            {
                new JObject
                {
                    ["id"] = "test001",
                    ["key"] = "TEST_KEY",
                    ["word"] = "Test Word"
                }
            }
        };

        bool oldStructureDetection = oldStructureJson["keywordPkg"]?["data"] is JArray oldArray && oldArray.Count > 0;
        LogTest("Old structure should fail", oldStructureDetection.ToString(), "False");

        Debug.Log("✅ [STRUCTURE_TEST_1] DetermineTargetFile logic test completed");
    }

    /// <summary>
    /// Test the ProcessKeywordImport logic with the corrected nested structure
    /// </summary>
    private void TestProcessKeywordImportLogic()
    {
        Debug.Log("🔍 [STRUCTURE_TEST_2] Testing ProcessKeywordImport Logic...");

        // Create test JSON with correct nested structure
        var testJson = new JObject
        {
            ["keywordPkg"] = new JObject
            {
                ["exportedDateTime"] = "31/07/2025_12:43:52",
                ["appVersion"] = "9.10.54",
                ["data"] = new JArray
                {
                    new JObject
                    {
                        ["id"] = "test001",
                        ["key"] = "TEST_KEY_1",
                        ["word"] = "Test Word 1",
                        ["keywordTags"] = new JArray { "TEST" },
                        ["revisionCounterWordAI"] = 5,
                        ["extraField"] = "should be ignored"
                    },
                    new JObject
                    {
                        ["id"] = "test002",
                        ["key"] = "TEST_KEY_2",
                        ["word"] = "Test Word 2",
                        ["keywordTags"] = new JArray { "TEST", "SAMPLE" },
                        ["revisionCounterWordAI"] = 10
                    }
                }
            }
        };

        // Test accessing keywordPkg
        var keywordPkg = testJson["keywordPkg"];
        LogTest("KeywordPkg exists", (keywordPkg != null).ToString(), "True");

        // Test accessing nested data array
        var dataArray = keywordPkg?["data"] as JArray;
        LogTest("Data array exists", (dataArray != null).ToString(), "True");
        LogTest("Data array count", dataArray?.Count.ToString() ?? "0", "2");

        // Test metadata extraction
        var exportedDateTime = keywordPkg?["exportedDateTime"]?.ToString();
        var appVersion = keywordPkg?["appVersion"]?.ToString();
        LogTest("Exported DateTime", exportedDateTime ?? "null", "31/07/2025_12:43:52");
        LogTest("App Version", appVersion ?? "null", "9.10.54");

        // Test keyword field extraction
        if (dataArray != null && dataArray.Count > 0)
        {
            var firstKeyword = dataArray[0];
            var id = firstKeyword["id"]?.ToString();
            var key = firstKeyword["key"]?.ToString();
            var word = firstKeyword["word"]?.ToString();
            var tags = firstKeyword["keywordTags"] as JArray;

            LogTest("First keyword ID", id ?? "null", "test001");
            LogTest("First keyword key", key ?? "null", "TEST_KEY_1");
            LogTest("First keyword word", word ?? "null", "Test Word 1");
            LogTest("First keyword tags count", tags?.Count.ToString() ?? "0", "1");

            // Test that extra fields exist but would be ignored
            var revisionCounter = firstKeyword["revisionCounterWordAI"];
            var extraField = firstKeyword["extraField"];
            LogTest("Revision counter exists", (revisionCounter != null).ToString(), "True");
            LogTest("Extra field exists", (extraField != null).ToString(), "True");
        }

        Debug.Log("✅ [STRUCTURE_TEST_2] ProcessKeywordImport logic test completed");
    }

    /// <summary>
    /// Test metadata extraction from the nested structure
    /// </summary>
    private void TestMetadataExtraction()
    {
        Debug.Log("🔍 [STRUCTURE_TEST_3] Testing Metadata Extraction...");

        // Test with metadata
        var jsonWithMetadata = new JObject
        {
            ["keywordPkg"] = new JObject
            {
                ["exportedDateTime"] = "01/08/2025_15:30:45",
                ["appVersion"] = "9.11.0",
                ["data"] = new JArray()
            }
        };

        var pkg = jsonWithMetadata["keywordPkg"];
        var dateTime = pkg?["exportedDateTime"]?.ToString();
        var version = pkg?["appVersion"]?.ToString();

        LogTest("Metadata DateTime", dateTime ?? "null", "01/08/2025_15:30:45");
        LogTest("Metadata Version", version ?? "null", "9.11.0");

        // Test without metadata
        var jsonWithoutMetadata = new JObject
        {
            ["keywordPkg"] = new JObject
            {
                ["data"] = new JArray()
            }
        };

        var pkgNoMeta = jsonWithoutMetadata["keywordPkg"];
        var noDateTime = pkgNoMeta?["exportedDateTime"]?.ToString();
        var noVersion = pkgNoMeta?["appVersion"]?.ToString();

        LogTest("No metadata DateTime", string.IsNullOrEmpty(noDateTime).ToString(), "True");
        LogTest("No metadata Version", string.IsNullOrEmpty(noVersion).ToString(), "True");

        Debug.Log("✅ [STRUCTURE_TEST_3] Metadata extraction test completed");
    }

    /// <summary>
    /// Test that extra fields are properly identified and would be ignored
    /// </summary>
    private void TestExtraFieldHandling()
    {
        Debug.Log("🔍 [STRUCTURE_TEST_4] Testing Extra Field Handling...");

        var keywordWithExtraFields = new JObject
        {
            ["id"] = "test001",
            ["key"] = "TEST_KEY",
            ["word"] = "Test Word",
            ["keywordTags"] = new JArray { "TEST" },
            ["revisionCounterWordAI"] = 15,
            ["extraField1"] = "ignored",
            ["extraField2"] = 12345,
            ["extraField3"] = true
        };

        // Simulate the extra field detection logic from ProcessKeywordImport
        var extraFields = keywordWithExtraFields.Properties()
            .Where(p => p.Name != "id" && p.Name != "key" && p.Name != "word" && p.Name != "keywordTags")
            .Select(p => p.Name)
            .ToArray();

        LogTest("Extra fields count", extraFields.Length.ToString(), "4");
        LogTest("Contains revisionCounterWordAI", extraFields.Contains("revisionCounterWordAI").ToString(), "True");
        LogTest("Contains extraField1", extraFields.Contains("extraField1").ToString(), "True");
        LogTest("Contains extraField2", extraFields.Contains("extraField2").ToString(), "True");
        LogTest("Contains extraField3", extraFields.Contains("extraField3").ToString(), "True");

        // Test that core fields are not in extra fields
        LogTest("Core field 'id' not in extras", (!extraFields.Contains("id")).ToString(), "True");
        LogTest("Core field 'key' not in extras", (!extraFields.Contains("key")).ToString(), "True");
        LogTest("Core field 'word' not in extras", (!extraFields.Contains("word")).ToString(), "True");
        LogTest("Core field 'keywordTags' not in extras", (!extraFields.Contains("keywordTags")).ToString(), "True");

        Debug.Log($"📋 [STRUCTURE_TEST_4] Extra fields detected: {string.Join(", ", extraFields)}");
        Debug.Log("✅ [STRUCTURE_TEST_4] Extra field handling test completed");
    }

    /// <summary>
    /// Test modifier structure parsing and validation
    /// </summary>
    private void TestModifierStructure()
    {
        Debug.Log("🔍 [STRUCTURE_TEST_5] Testing Modifier Structure...");

        // Create test JSON with modifier structure
        var testJson = new JObject
        {
            ["modifierListPkg"] = new JObject
            {
                ["exportedDateTime"] = "31/07/2025_12:43:52",
                ["appVersion"] = "9.10.54",
                ["data"] = new JArray
                {
                    new JObject
                    {
                        ["id"] = "MO0",
                        ["skill"] = "Inteligência",
                        ["description"] = "Influencia a precisão...",
                        ["acronym"] = "Qi",
                        ["revisionCounterSkillAI"] = 5,
                        ["revisionCounterDescriptionAI"] = 3,
                        ["isReviewedDescription"] = true
                    },
                    new JObject
                    {
                        ["id"] = "MO1",
                        ["skill"] = "Sorte",
                        ["description"] = "Afeta a chance de críticos...",
                        ["acronym"] = "So",
                        ["revisionCounterSkillAI"] = 2
                    }
                }
            }
        };

        // Test modifier data detection
        bool hasModifierData = testJson["modifierListPkg"]?["data"] is JArray modifierArray && modifierArray.Count > 0;
        LogTest("Modifier data detection", hasModifierData.ToString(), "True");

        // Test accessing modifier package
        var modifierPkg = testJson["modifierListPkg"];
        LogTest("ModifierListPkg exists", (modifierPkg != null).ToString(), "True");

        // Test accessing nested data array
        var dataArray = modifierPkg?["data"] as JArray;
        LogTest("Modifier data array exists", (dataArray != null).ToString(), "True");
        LogTest("Modifier data array count", dataArray?.Count.ToString() ?? "0", "2");

        // Test metadata extraction
        var exportedDateTime = modifierPkg?["exportedDateTime"]?.ToString();
        var appVersion = modifierPkg?["appVersion"]?.ToString();
        LogTest("Modifier exported DateTime", exportedDateTime ?? "null", "31/07/2025_12:43:52");
        LogTest("Modifier app version", appVersion ?? "null", "9.10.54");

        // Test modifier field extraction
        if (dataArray != null && dataArray.Count > 0)
        {
            var firstModifier = dataArray[0];
            var id = firstModifier["id"]?.ToString();
            var skill = firstModifier["skill"]?.ToString();
            var description = firstModifier["description"]?.ToString();
            var acronym = firstModifier["acronym"]?.ToString();

            LogTest("First modifier ID", id ?? "null", "MO0");
            LogTest("First modifier skill", skill ?? "null", "Inteligência");
            LogTest("First modifier acronym", acronym ?? "null", "Qi");
            LogTest("First modifier has description", (!string.IsNullOrEmpty(description)).ToString(), "True");

            // Test that extra fields exist but would be ignored
            var revisionSkill = firstModifier["revisionCounterSkillAI"];
            var revisionDesc = firstModifier["revisionCounterDescriptionAI"];
            var isReviewed = firstModifier["isReviewedDescription"];
            LogTest("Revision skill counter exists", (revisionSkill != null).ToString(), "True");
            LogTest("Revision description counter exists", (revisionDesc != null).ToString(), "True");
            LogTest("Is reviewed description exists", (isReviewed != null).ToString(), "True");
        }

        // Test modifier extra field detection
        if (dataArray != null && dataArray.Count > 0)
        {
            var modifier = dataArray[0] as JObject;
            if (modifier != null)
            {
                var extraFields = modifier.Properties()
                    .Where(p => p.Name != "id" && p.Name != "skill" && p.Name != "description" && p.Name != "acronym")
                    .Select(p => p.Name)
                    .ToArray();

                LogTest("Modifier extra fields count", extraFields.Length.ToString(), "3");
                LogTest("Contains revisionCounterSkillAI", extraFields.Contains("revisionCounterSkillAI").ToString(), "True");
                LogTest("Contains revisionCounterDescriptionAI", extraFields.Contains("revisionCounterDescriptionAI").ToString(), "True");
                LogTest("Contains isReviewedDescription", extraFields.Contains("isReviewedDescription").ToString(), "True");

                Debug.Log($"📋 [STRUCTURE_TEST_5] Modifier extra fields: {string.Join(", ", extraFields)}");
            }
        }

        Debug.Log("✅ [STRUCTURE_TEST_5] Modifier structure test completed");
    }

    /// <summary>
    /// Helper method to log test results with consistent formatting
    /// </summary>
    private void LogTest(string testName, string actual, string expected)
    {
        bool passed = actual == expected;
        string status = passed ? "✅ PASS" : "❌ FAIL";
        string message = $"{status} [{testName}] Expected: '{expected}', Actual: '{actual}'";

        if (passed)
        {
            Debug.Log(message);
        }
        else
        {
            Debug.LogError(message);
        }
    }

    /// <summary>
    /// Create a test file with the corrected structure for manual testing
    /// </summary>
    [ContextMenu("Create Test Import File")]
    public void CreateTestImportFile()
    {
        string testData = @"{
  ""keywordPkg"": {
    ""exportedDateTime"": ""01/08/2025_16:00:00"",
    ""appVersion"": ""9.10.54"",
    ""data"": [
      {
        ""id"": ""test_corrected_001"",
        ""key"": ""CORRECTED_TEST_1"",
        ""word"": ""Teste Corrigido 1"",
        ""keywordTags"": [""TEST"", ""CORRECTED""],
        ""revisionCounterWordAI"": 1
      },
      {
        ""id"": ""test_corrected_002"",
        ""key"": ""CORRECTED_TEST_2"",
        ""word"": ""Teste Corrigido 2"",
        ""keywordTags"": [""TEST"", ""CORRECTED""],
        ""revisionCounterWordAI"": 2,
        ""extraField"": ""This will be ignored""
      }
    ]
  },
  ""modifierListPkg"": {
    ""exportedDateTime"": ""01/08/2025_16:00:00"",
    ""appVersion"": ""9.10.54"",
    ""data"": [
      {
        ""id"": ""TEST_MOD_001"",
        ""skill"": ""Teste Modificador 1"",
        ""description"": ""Descrição do teste de modificador 1"",
        ""acronym"": ""Tm1"",
        ""revisionCounterSkillAI"": 1,
        ""revisionCounterDescriptionAI"": 1,
        ""isReviewedDescription"": false
      },
      {
        ""id"": ""TEST_MOD_002"",
        ""skill"": ""Teste Modificador 2"",
        ""description"": ""Descrição do teste de modificador 2"",
        ""acronym"": ""Tm2"",
        ""revisionCounterSkillAI"": 2,
        ""extraField"": ""This will also be ignored""
      }
    ]
  }
}";

        string filePath = Path.Combine(Application.temporaryCachePath, "corrected_structure_test.json");
        File.WriteAllText(filePath, testData);

        Debug.Log($"📁 [STRUCTURE_TEST] Created corrected structure test file at: {filePath}");
        Debug.Log("📋 [STRUCTURE_TEST] Use this file to manually test the import functionality");
    }
}
